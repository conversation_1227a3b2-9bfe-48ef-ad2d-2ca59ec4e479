require 'test_helper'

class Scout::ChatRequestsControllerTest < ActionDispatch::IntegrationTest
  def setup
    # Clean up any existing data
    ChatRequest.destroy_all
    TalentProfile.destroy_all # Added for completeness
    User.destroy_all # Use destroy_all to trigger callbacks

    @scout =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Scout',
        last_name: 'Test',
        scout_signup_completed: true,
        verified: true, # Ensure user is verified for sign-in
        onboarding_completed: true,
        onboarding_step: 'completed',
      )

    @talent =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'Talent',
        last_name: 'Test',
        talent_signup_completed: true,
        verified: true, # Ensure user is verified
      )

    # Create talent profile for the talent user
    @talent_profile =
      @talent.create_talent_profile!(
        headline: 'Test Ghostwriter',
        about: 'Test about section',
      )

    # Create an organization and associate scout
    @organization = Organization.create!(name: "Scout's Test Org")
    OrganizationMembership.create!(
      user: @scout,
      organization: @organization,
      org_role: 'admin',
    )
    @scout.update!(last_logged_in_organization_id: @organization.id)

    # Sign in as scout
    post sign_in_url,
         params: {
           email: @scout.email,
           password: 'password123456',
         }
  end

  def teardown
    ChatRequest.destroy_all
    TalentProfile.destroy_all # Added for completeness
    User.destroy_all # Use destroy_all to trigger callbacks
  end

  test 'should get new chat request modal' do
    get scout_new_chat_request_path,
        params: {
          talent_user_id: @talent.id,
        },
        headers: {
          'Accept' => 'text/vnd.turbo-stream.html',
        }

    assert_response :success
    assert_includes response.body, 'Request to Chat with'
    assert_includes response.body, @talent.name
  end

  test 'should create chat request without pitch and return success response' do
    assert_difference 'ChatRequest.count', 1 do
      post scout_create_chat_request_path,
           params: {
             talent_user_id: @talent.id,
             chat_request: {
               pitch: '',
             },
           },
           headers: {
             'Accept' => 'text/vnd.turbo-stream.html',
           }
    end

    assert_response :success
    chat_request = ChatRequest.last
    assert_equal @scout, chat_request.scout
    assert_equal @talent, chat_request.talent
    assert_equal 'pending', chat_request.status
    assert_equal '', chat_request.pitch

    # Verify response contains success partial and talent card update
    assert_includes response.body, 'data-chat-request-success="true"'
    assert_includes response.body, 'talent_card_'
  end

  test 'should create chat request with pitch' do
    pitch_message = 'Hi! I would love to work with you on my project.'

    assert_difference 'ChatRequest.count', 1 do
      post scout_create_chat_request_path,
           params: {
             talent_user_id: @talent.id,
             chat_request: {
               pitch: pitch_message,
             },
           },
           headers: {
             'Accept' => 'text/vnd.turbo-stream.html',
           }
    end

    assert_response :success
    chat_request = ChatRequest.last
    assert_equal @scout, chat_request.scout
    assert_equal @talent, chat_request.talent
    assert_equal 'pending', chat_request.status
    assert_equal pitch_message, chat_request.pitch

    # Verify response contains success partial and talent card update
    assert_includes response.body, 'data-chat-request-success="true"'
    assert_includes response.body, 'talent_card_'
  end

  test 'should not create duplicate pending requests' do
    # Create first request
    @scout.sent_chat_requests.create!(talent: @talent)

    assert_no_difference 'ChatRequest.count' do
      post scout_create_chat_request_path,
           params: {
             talent_user_id: @talent.id,
             chat_request: {
               pitch: 'Another request',
             },
           },
           headers: {
             'Accept' => 'text/vnd.turbo-stream.html',
           }
    end

    assert_response :success
    assert_includes response.body, 'Request Already Sent'
  end

  test 'should show existing request error in modal' do
    # Create first request
    @scout.sent_chat_requests.create!(talent: @talent)

    get scout_new_chat_request_path,
        params: {
          talent_user_id: @talent.id,
        },
        headers: {
          'Accept' => 'text/vnd.turbo-stream.html',
        }

    assert_response :success
    assert_includes response.body, 'Request Already Sent'
    assert_includes response.body, @talent.name
  end
end
