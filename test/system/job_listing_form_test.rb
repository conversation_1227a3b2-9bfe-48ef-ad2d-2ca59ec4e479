require 'application_system_test_case'

class JobListingFormTest < ApplicationSystemTestCase
  # Don't load fixtures to avoid foreign key issues
  self.use_transactional_tests = false

  def self.fixtures(*args)
    # Override to prevent fixture loading
  end
  setup do
    # Create a test user with scout privileges
    @user =
      User.create!(
        email: '<EMAIL>',
        password: 'password123123',
        verified: true,
        first_name: 'Test',
        last_name: 'Scout',
        time_zone: 'UTC',
        onboarding_completed: true,
        onboarding_step: 'completed',
        signup_intent: 'scout',
        scout_signup_completed: true,
        talent_signup_completed: false,
      )

    # Create an organization for the scout
    @organization = Organization.create!(name: 'Test Organization')

    # Add scout to organization
    OrganizationMembership.create!(
      user: @user,
      organization: @organization,
      org_role: 'admin',
    )

    @user.update!(last_logged_in_organization_id: @organization.id)
  end

  teardown do
    # Clean up test data
    OrganizationMembership.delete_all
    UserRole.delete_all # Ensure user roles are cleared first
    ImpersonationLog.delete_all # Ensure impersonation logs are cleared first
    Session.delete_all # Ensure sessions are cleared first
    Organization.delete_all
    User.delete_all
  end

  test 'job listing form shows one question at a time' do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Log in'
    save_and_open_screenshot # Add this to debug login

    # Navigate to job creation form
    visit new_scout_job_path

    # Verify initial step shows only category selection
    assert_text 'Tell us about the ghostwriter you need'
    assert_text 'What type of ghostwriter are you looking to hire?'
    assert_selector "select[name='job[job_category]']"

    # Verify topics section is not visible initially
    assert_no_text 'What topics should they write about?'

    # Select newsletter category
    select 'Newsletter', from: 'job[job_category]'

    # Click Continue to go to topics step
    click_button 'Continue'

    # Verify we're now on topics selection step
    assert_text 'What topics should they write about?'
    assert_text 'Select all that apply'

    # Verify category selection is no longer visible
    assert_no_text 'What type of ghostwriter are you looking to hire?'

    # Select some topics
    check 'Artificial Intelligence'
    check 'Marketing & Sales'

    # Click Continue to go to newsletter steps
    click_button 'Continue'

    # Verify we're now on newsletter goal step
    assert_text 'Newsletter Goal'
    assert_text "What's the main goal for your newsletter?"
    assert_text 'Selected topics:'
    assert_text 'Artificial Intelligence'
    assert_text 'Marketing & Sales'

    # Verify only goal question is visible, not frequency/length/budget
    assert_no_text 'How often do you want to send your newsletter?'
    assert_no_text 'How long should each newsletter be?'
    assert_no_text "What's your approximate monthly budget"

    # Select a goal
    select 'Build brand', from: 'job[outcome]'

    # Click Continue to go to frequency step
    click_button 'Continue'

    # Verify we're now on newsletter frequency step
    assert_text 'Newsletter Frequency'
    assert_text 'How often do you want to send your newsletter?'

    # Verify goal question is no longer visible
    assert_no_text "What's the main goal for your newsletter?"

    # Select frequency
    choose 'Weekly'

    # Click Continue to go to length step
    click_button 'Continue'

    # Verify we're now on newsletter length step
    assert_text 'Newsletter Length'
    assert_text 'How long should each newsletter be?'

    # Verify frequency question is no longer visible
    assert_no_text 'How often do you want to send your newsletter?'

    # Select length
    choose '300-600 words'

    # Click Continue to go to job details
    click_button 'Continue'

    # Verify we're now on job details step
    assert_text 'Job Listing Details'
    assert_text 'Write the title for your job post'

    # Verify budget question is no longer visible
    assert_no_text "What's your approximate monthly budget range"
  end

  test 'continue button works properly' do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Log in'

    # Navigate to job creation form
    visit new_scout_job_path

    # Try clicking Continue without selecting category (should show validation)
    click_button 'Continue'

    # Should still be on category step with error
    assert_text 'Tell us about the ghostwriter you need'

    # Select category and continue should work
    select 'Newsletter', from: 'job[job_category]'
    click_button 'Continue'

    # Should now be on topics step
    assert_text 'What topics should they write about?'

    # Continue without selecting topics should still work (topics are optional)
    click_button 'Continue'

    # Should now be on newsletter goal step
    assert_text 'Newsletter Goal'
  end

  test 'newsletter category shows 3 specific branching questions' do
    # Sign in as scout
    visit sign_in_path
    fill_in 'Email', with: @user.email
    fill_in 'Password', with: 'password123123'
    click_button 'Log in'

    # Navigate to job creation form
    visit new_scout_job_path

    # Select newsletter category
    select 'Newsletter', from: 'job[job_category]'
    click_button 'Continue'

    # Skip topics
    click_button 'Continue'

    # Verify all 3 newsletter questions exist in sequence
    # 1. Goal
    assert_text 'Newsletter Goal'
    select 'Build brand', from: 'job[outcome]'
    click_button 'Continue'

    # 2. Frequency
    assert_text 'Newsletter Frequency'
    choose 'Weekly'
    click_button 'Continue'

    # 3. Length
    assert_text 'Newsletter Length'
    choose '300-600 words'
    click_button 'Continue'

    # Should now proceed to job details after all 3 questions
    assert_text 'Job Listing Details'
  end
end
