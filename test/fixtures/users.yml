# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: users
#
#  id                             :bigint           not null, primary key
#  email                          :string           not null
#  first_name                     :string
#  last_name                      :string
#  onboarding_completed           :boolean          default(FALSE)
#  onboarding_step                :string           default("personal")
#  password_digest                :string           not null
#  scout_signup_completed         :boolean          default(FALSE)
#  signup_intent                  :string
#  talent_signup_completed        :boolean          default(FALSE)
#  time_zone                      :string
#  verification_email_sent_at     :datetime
#  verified                       :boolean          default(FALSE), not null
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  last_logged_in_organization_id :integer
#
# Indexes
#
#  index_users_on_email                           (email) UNIQUE
#  index_users_on_last_logged_in_organization_id  (last_logged_in_organization_id)
#
lazaro_nixon:
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("Secret1*3*5*") %>
  verified: true
  onboarding_completed: true
  onboarding_step: "completed"

one:
  id: <%= ActiveRecord::FixtureSet.identify(:one) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("password") %>
  verified: true
  first_name: User
  last_name: One
  onboarding_completed: true
  onboarding_step: "completed"

two:
  id: <%= ActiveRecord::FixtureSet.identify(:two) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("password") %>
  verified: true
  first_name: User
  last_name: Two
  onboarding_completed: true
  onboarding_step: "completed"

admin:
  id: <%= ActiveRecord::FixtureSet.identify(:admin) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("Secret1*3*5*") %>
  verified: true
  first_name: Admin
  last_name: User
  onboarding_completed: true
  onboarding_step: "completed"

super_admin:
  id: <%= ActiveRecord::FixtureSet.identify(:super_admin) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("Secret1*3*5*") %>
  verified: true
  first_name: Super
  last_name: Admin
  onboarding_completed: true
  onboarding_step: "completed"

scout:
  id: <%= ActiveRecord::FixtureSet.identify(:scout) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("Secret1*3*5*") %>
  verified: true
  first_name: Scout
  last_name: User
  onboarding_completed: true
  onboarding_step: "completed"
  scout_signup_completed: true

talent:
  id: <%= ActiveRecord::FixtureSet.identify(:talent) %>
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create("Secret1*3*5*") %>
  verified: true
  first_name: Talent
  last_name: User
  onboarding_completed: false
  onboarding_step: "personal"
  talent_signup_completed: true
