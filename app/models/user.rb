# == Schema Information
#
# Table name: users
#
#  id                             :bigint           not null, primary key
#  email                          :string           not null
#  first_name                     :string
#  last_name                      :string
#  onboarding_completed           :boolean          default(FALSE)
#  onboarding_step                :string           default("personal")
#  password_digest                :string           not null
#  scout_signup_completed         :boolean          default(FALSE)
#  signup_intent                  :string
#  talent_signup_completed        :boolean          default(FALSE)
#  time_zone                      :string
#  verification_email_sent_at     :datetime
#  verified                       :boolean          default(FALSE), not null
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  last_logged_in_organization_id :integer
#
# Indexes
#
#  index_users_on_email                           (email) UNIQUE
#  index_users_on_last_logged_in_organization_id  (last_logged_in_organization_id)
#
class User < ApplicationRecord
  include AdminPermissions

  has_person_name
  has_secure_password
  pay_customer # Include the Pay::Billable concern and associations

  has_one :talent_profile, dependent: :destroy
  has_one_attached :avatar

  has_many :organization_memberships, dependent: :destroy
  has_many :organizations, through: :organization_memberships
  has_many :user_roles, dependent: :destroy
  has_many :roles, through: :user_roles
  has_many :conversation_participants
  has_many :conversations, through: :conversation_participants
  has_many :messages
  has_many :job_applications
  has_many :applied_jobs, through: :job_applications, source: :job
  has_many :job_invitations, dependent: :destroy
  has_many :saved_jobs, dependent: :destroy
  has_many :saved_jobs_list, through: :saved_jobs, source: :job
  has_many :talent_bookmarks, dependent: :destroy
  has_many :bookmarked_talents,
           -> { distinct },
           through: :talent_bookmarks,
           source: :talent_profile

  # Chat request associations
  has_many :sent_chat_requests,
           class_name: 'ChatRequest',
           foreign_key: 'scout_id',
           dependent: :destroy
  has_many :received_chat_requests,
           class_name: 'ChatRequest',
           foreign_key: 'talent_id',
           dependent: :destroy

  generates_token_for :email_verification, expires_in: 2.days do
    email
  end

  generates_token_for :password_reset, expires_in: 20.minutes do
    password_salt.last(10)
  end

  has_many :sessions, dependent: :destroy

  validates :email,
            presence: true,
            uniqueness: true,
            format: {
              with: URI::MailTo::EMAIL_REGEXP,
            }

  validates :password, allow_nil: true, length: { minimum: 12 }

  # validates :time_zone, inclusion: { in: ActiveSupport::TimeZone.all.map(&:name), message: "is not a valid time zone" }

  normalizes :email, with: -> { _1.strip.downcase }

  before_validation if: :email_changed?, on: :update do
    self.verified = false
  end

  after_update if: :password_digest_previously_changed? do
    sessions.where.not(id: Current.session).delete_all
  end

  def has_role?(role_name)
    roles.exists?(name: role_name.to_s)
  end

  def superadmin?
    has_role?(:superadmin)
  end

  def full_name
    name.full
  end

  def avatar_color
    colors = [%w[bg-stone-100 text-stone-600]]

    # Use the user's ID or name to consistently get the same color
    index = (id || full_name.hash).abs % colors.length
    colors[index]
  end

  def initials
    if first_name.present? && last_name.present?
      "#{first_name.first}#{last_name.first}"
    elsif first_name.present?
      first_name.first
    elsif last_name.present?
      last_name.first
    else
      'XX' # Default if no name is present
    end
  end

  def pay_should_sync_customer?
    # super will invoke Pay's default (e-mail changed)
    # Check if first_name or last_name changed
    super || self.saved_change_to_first_name? || self.saved_change_to_last_name?
  end

  def stripe_attributes(pay_customer)
    {
      metadata: {
        pay_customer_id: pay_customer.id,
        user_id: id, # or pay_customer.owner_id
      },
    }
  end

  # Chat request helper methods
  def has_pending_chat_request_with?(talent_user)
    sent_chat_requests.pending.exists?(talent: talent_user)
  end
end
