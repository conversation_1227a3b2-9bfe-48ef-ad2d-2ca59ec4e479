# == Schema Information
#
# Table name: chat_requests
#
#  id           :bigint           not null, primary key
#  accepted_at  :datetime
#  declined_at  :datetime
#  pitch        :text
#  requested_at :datetime
#  status       :integer          default("pending"), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  scout_id     :bigint           not null
#  talent_id    :bigint           not null
#
# Indexes
#
#  index_chat_requests_on_scout_id                (scout_id)
#  index_chat_requests_on_scout_id_and_talent_id  (scout_id,talent_id) UNIQUE
#  index_chat_requests_on_talent_id               (talent_id)
#
# Foreign Keys
#
#  fk_rails_...  (scout_id => users.id)
#  fk_rails_...  (talent_id => users.id)
#
class ChatRequest < ApplicationRecord
  belongs_to :scout, class_name: 'User'
  belongs_to :talent, class_name: 'User'

  enum :status, { pending: 0, accepted: 1, declined: 2 }

  # Scopes for madmin filtering
  scope :recent, -> { where('requested_at > ?', 7.days.ago) }

  validates :scout_id,
            uniqueness: {
              scope: %i[talent_id status],
              conditions: -> { where(status: :pending) },
              message: 'already has a pending chat request with this talent',
            }

  before_create :set_requested_at

  def accept!
    update!(status: :accepted, accepted_at: Time.current)
  end

  def decline!
    update!(status: :declined, declined_at: Time.current)
  end

  private

  def set_requested_at
    self.requested_at = Time.current
  end
end
