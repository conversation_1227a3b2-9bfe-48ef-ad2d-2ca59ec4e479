# == Schema Information
#
# Table name: talent_profiles
#
#  id                     :bigint           not null, primary key
#  about                  :text
#  achievement_badges     :string           default([]), is an Array
#  availability_status    :integer          default("available")
#  bio                    :text
#  ghostwriter_type       :string           default([]), is an Array
#  headline               :string
#  instagram_url          :string
#  is_agency              :boolean          default(FALSE)
#  is_premium             :boolean          default(FALSE)
#  linkedin_url           :string
#  location               :string
#  location_preference    :integer          default("north_america")
#  looking_for            :text
#  niches                 :string           default([]), is an Array
#  outcomes               :string           default([]), is an Array
#  platform_choice        :string
#  portfolio_link         :string
#  price_range_max        :decimal(10, 2)
#  price_range_min        :decimal(10, 2)
#  pricing_model          :integer          default("hourly")
#  skills                 :string           default([]), is an Array
#  social_media_specialty :string           default([]), is an Array
#  threads_url            :string
#  vsl_link               :string
#  website_url            :string
#  x_url                  :string
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  user_id                :bigint           not null
#
# Indexes
#
#  index_talent_profiles_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class TalentProfile < ApplicationRecord
  belongs_to :user
  has_many :talent_bookmarks, dependent: :destroy

  def bookmarked_by?(user)
    return false unless user.present?
    talent_bookmarks.exists?(user: user)
  end

  enum :pricing_model,
       { hourly: 0, fixed_price: 1, retainer: 2, project_based: 3 }
  enum :availability_status, { available: 0, limited: 1, unavailable: 2 }
  enum :location_preference,
       {
         north_america: 0,
         united_kingdom: 1,
         europe: 2,
         africa: 3,
         asia: 4,
         south_america: 5,
         india: 6,
       }

  searchkick word_middle: %i[
               skills
               bio
               about
               looking_for
               location
               headline
               user_name
             ],
             callbacks: :async,
             filterable: %i[
               skills
               location
               availability_status
               ghostwriter_type
               niches
               location_preference
               pricing_model
               is_agency
               is_premium
             ]

  def search_data
    {
      user_name: user&.name&.full,
      skills: skills || [],
      bio: bio,
      about: about,
      looking_for: looking_for,
      location: location,
      headline: headline,
      availability_status: availability_status,
      ghostwriter_type: ghostwriter_type || [],
      niches: niches || [],
      location_preference: location_preference,
      pricing_model: pricing_model,
      is_agency: is_agency,
      is_premium: is_premium,
      created_at: created_at,
      updated_at: updated_at,
    }
  end

  has_many :talent_bookmarks, dependent: :destroy
  has_many :bookmarked_by_users, through: :talent_bookmarks, source: :user
  has_many :talent_notes, dependent: :destroy

  # Active Storage attachments for resume, cover letter, etc.
  has_many_attached :attachments

  def open_to_new_clients?
    available? || limited?
  end
end
