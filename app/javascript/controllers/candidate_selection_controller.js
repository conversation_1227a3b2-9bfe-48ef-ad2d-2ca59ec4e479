import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static values = {
    id: Number,
    name: String,
    jobTitle: String,
    status: String,
    avatarUrl: String,
  };

  static params = {
    applicantId: String,
    jobId: String,
  };

  showDetails(event) {
    event.preventDefault();

    // Highlight the selected candidate
    this.highlightSelectedCandidate();

    // Fetch candidate details via Turbo Stream
    const url = `/scout/applicants/${this.idValue}/details`;

    // Use Turbo Frame's native functionality instead of Turbo.visit
    const frame = document.getElementById("candidate-details-container");
    if (frame) {
      frame.src = url;
    } else {
      // Fallback to regular fetch if frame not found
      fetch(url, {
        headers: {
          Accept: "text/vnd.turbo-stream.html",
        },
      });
    }
  }

  highlightSelectedCandidate() {
    // Remove highlight from previously selected candidate
    document.querySelectorAll(".candidate-selected").forEach((el) => {
      el.classList.remove("candidate-selected");
      el.classList.add("border-stone-200");
      el.classList.remove("border-[#6100FF]");
      el.classList.remove("bg-purple-50"); // Ensure background class is removed
    });

    // Add highlight to current candidate
    this.element.classList.add("candidate-selected");
    this.element.classList.remove("border-stone-200");
    this.element.classList.remove("border-b");
    this.element.classList.add("border");
    this.element.classList.add("border-[#6100FF]");
    this.element.classList.add("bg-purple-50"); // Add background class separately
  }

  // Stop event propagation for message button clicks
  stopPropagation(event) {
    event.stopPropagation();
  }

  // Open message modal
  openMessageModal(event) {
    event.preventDefault();
    event.stopPropagation();

    // Get applicant ID and job ID from data attributes
    const applicantId = event.currentTarget.dataset.applicantId;
    const jobId = event.currentTarget.dataset.jobId;

    // Bail early if we don't have the required IDs
    if (!applicantId || !jobId) {
      console.error("Missing applicant or job ID for message modal");
      return;
    }

    // Construct URL for modal conversation
    const url = `/scout/conversations/modal/${applicantId}?job_id=${jobId}`;

    // Fetch conversation content and update modal
    fetch(url, {
      headers: {
        Accept: "text/html, application/xhtml+xml",
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text();
      })
      .then((html) => {
        // Update modal content
        const modalContent = document.getElementById("message-modal-content");
        if (modalContent) {
          modalContent.innerHTML = html;

          // Show modal by dispatching an event to modal controller
          const modalController = document.querySelector(
            '[data-controller="modal"]'
          );
          if (modalController) {
            modalController.dispatchEvent(new CustomEvent("show-modal"));
          } else {
            console.error("Modal controller element not found");
          }
        } else {
          console.error("Modal content element not found");
        }
      })
      .catch((error) => {
        console.error("Error loading conversation modal:", error);
      });
  }
}
