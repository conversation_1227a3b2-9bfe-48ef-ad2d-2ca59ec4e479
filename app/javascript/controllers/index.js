// This file is auto-generated by ./bin/rails stimulus:manifest:update
// Run that command whenever you add a new controller or create them with
// ./bin/rails generate stimulus controllerName

import { application } from "./application"

import AccordionController from "./accordion_controller"
application.register("accordion", AccordionController)

import ApplicationFilterController from "./application_filter_controller"
application.register("application-filter", ApplicationFilterController)

import ApplicationWizardController from "./application_wizard_controller"
application.register("application-wizard", ApplicationWizardController)

import BookmarkButtonController from "./bookmark_button_controller"
application.register("bookmark-button", BookmarkButtonController)

import CandidateDetailsController from "./candidate_details_controller"
application.register("candidate-details", CandidateDetailsController)

import CandidateSelectionController from "./candidate_selection_controller"
application.register("candidate-selection", CandidateSelectionController)

import Chat<PERSON>ontroller from "./chat_controller"
application.register("chat", ChatController)

import ChatRequestButtonController from "./chat_request_button_controller"
application.register("chat-request-button", ChatRequestButtonController)

import ChatRequestModalController from "./chat_request_modal_controller"
application.register("chat-request-modal", ChatRequestModalController)

import DropdownController from "./dropdown_controller"
application.register("dropdown", DropdownController)

import FilterPanelController from "./filter_panel_controller"
application.register("filter-panel", FilterPanelController)

import FormBranchingController from "./form_branching_controller"
application.register("form-branching", FormBranchingController)

import FormResetController from "./form_reset_controller"
application.register("form-reset", FormResetController)

import FormWizardController from "./form_wizard_controller"
application.register("form-wizard", FormWizardController)

import ImpersonationController from "./impersonation_controller"
application.register("impersonation", ImpersonationController)

import JobSearchController from "./job_search_controller"
application.register("job-search", JobSearchController)

import MessageController from "./message_controller"
application.register("message", MessageController)

import ModalController from "./modal_controller"
application.register("modal", ModalController)

import MultiStepFormController from "./multi_step_form_controller"
application.register("multi-step-form", MultiStepFormController)

import ProfileFormController from "./profile_form_controller"
application.register("profile-form", ProfileFormController)

import ScoutSidebarController from "./scout_sidebar_controller"
application.register("scout-sidebar", ScoutSidebarController)

import ScrollController from "./scroll_controller"
application.register("scroll", ScrollController)

import SegmentedController from "./segmented_controller"
application.register("segmented", SegmentedController)

import SidebarController from "./sidebar_controller"
application.register("sidebar", SidebarController)

import SidebarStatsController from "./sidebar_stats_controller"
application.register("sidebar-stats", SidebarStatsController)

import StageUpdateController from "./stage_update_controller"
application.register("stage-update", StageUpdateController)

import StatusUpdateController from "./status_update_controller"
application.register("status-update", StatusUpdateController)

import TalentFilterTagsController from "./talent_filter_tags_controller"
application.register("talent-filter-tags", TalentFilterTagsController)

import TalentSearchController from "./talent_search_controller"
application.register("talent-search", TalentSearchController)

import TalentSidebarController from "./talent_sidebar_controller"
application.register("talent-sidebar", TalentSidebarController)

import VerticalProgressController from "./vertical_progress_controller"
application.register("vertical-progress", VerticalProgressController)

import ViewToggleController from "./view_toggle_controller"
application.register("view-toggle", ViewToggleController)
