import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["selectAll", "recordCheckbox"]
  static values = { baseUrl: String }

  connect() {
    this.updateToolbar()
    this.setupEventListeners()
  }

  setupEventListeners() {
    // <PERSON>le select all checkbox
    this.selectAllTarget.addEventListener('change', (e) => {
      this.toggleAllRecords(e.target.checked)
    })

    // Handle individual record checkboxes
    this.recordCheckboxTargets.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.updateSelectAllState()
        this.updateToolbar()
      })
    })

    // Handle bulk action buttons
    document.addEventListener('click', (e) => {
      if (e.target.hasAttribute('data-bulk-action')) {
        e.preventDefault()
        this.handleBulkAction(e.target)
      }
    })

    // Handle clear selection button
    document.getElementById('clear-selection')?.addEventListener('click', () => {
      this.clearSelection()
    })
  }

  toggleAllRecords(checked) {
    this.recordCheckboxTargets.forEach(checkbox => {
      checkbox.checked = checked
    })
    this.updateToolbar()
  }

  updateSelectAllState() {
    const checkedCount = this.getSelectedRecords().length
    const totalCount = this.recordCheckboxTargets.length
    
    if (checkedCount === 0) {
      this.selectAllTarget.checked = false
      this.selectAllTarget.indeterminate = false
    } else if (checkedCount === totalCount) {
      this.selectAllTarget.checked = true
      this.selectAllTarget.indeterminate = false
    } else {
      this.selectAllTarget.checked = false
      this.selectAllTarget.indeterminate = true
    }
  }

  updateToolbar() {
    const selectedCount = this.getSelectedRecords().length
    const toolbar = document.getElementById('bulk-toolbar')
    const countElement = document.getElementById('selected-count')
    
    if (selectedCount > 0) {
      toolbar?.classList.remove('hidden')
      if (countElement) {
        countElement.textContent = `${selectedCount} selected`
      }
    } else {
      toolbar?.classList.add('hidden')
    }
  }

  clearSelection() {
    this.recordCheckboxTargets.forEach(checkbox => {
      checkbox.checked = false
    })
    this.selectAllTarget.checked = false
    this.selectAllTarget.indeterminate = false
    this.updateToolbar()
  }

  getSelectedRecords() {
    return this.recordCheckboxTargets
      .filter(checkbox => checkbox.checked)
      .map(checkbox => checkbox.dataset.recordId)
  }

  async handleBulkAction(button) {
    const action = button.dataset.bulkAction
    const needsConfirm = button.dataset.confirm === 'true'
    const confirmMessage = button.dataset.confirmMessage
    const selectedIds = this.getSelectedRecords()

    if (selectedIds.length === 0) {
      this.showNotification('Please select at least one record', 'error')
      return
    }

    if (needsConfirm && !confirm(confirmMessage || 'Are you sure?')) {
      return
    }

    // Show loading state
    button.disabled = true
    const originalText = button.textContent
    button.textContent = 'Processing...'

    try {
      const response = await this.performBulkAction(action, selectedIds)
      
      if (response.ok) {
        const result = await response.json()
        this.handleBulkActionSuccess(result)
        
        // Refresh the page to show updated data
        window.location.reload()
      } else {
        const error = await response.json()
        this.handleBulkActionError(error)
      }
    } catch (error) {
      console.error('Bulk action failed:', error)
      this.showNotification('An error occurred while performing the bulk action', 'error')
    } finally {
      // Restore button state
      button.disabled = false
      button.textContent = originalText
    }
  }

  async performBulkAction(action, selectedIds) {
    const url = `${this.baseUrlValue}/${action}`
    const formData = new FormData()
    formData.append('ids', selectedIds.join(','))
    
    // Add CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
    if (csrfToken) {
      formData.append('authenticity_token', csrfToken)
    }

    return fetch(url, {
      method: 'POST',
      body: formData,
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
  }

  handleBulkActionSuccess(result) {
    this.showNotification(result.message, 'success')
    
    if (result.errors && result.errors.length > 0) {
      console.warn('Some records failed:', result.errors)
      // Show detailed errors if needed
      this.showDetailedErrors(result.errors)
    }
    
    this.clearSelection()
  }

  handleBulkActionError(error) {
    this.showNotification(error.error || 'An error occurred', 'error')
  }

  showNotification(message, type = 'info') {
    // Create a simple notification
    const notification = document.createElement('div')
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${
      type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
      type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
      'bg-blue-100 text-blue-800 border border-blue-200'
    }`
    notification.textContent = message
    
    document.body.appendChild(notification)
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      notification.remove()
    }, 5000)
    
    // Allow manual dismissal
    notification.addEventListener('click', () => {
      notification.remove()
    })
  }

  showDetailedErrors(errors) {
    if (errors.length === 0) return
    
    // Create a modal or expandable section for detailed errors
    const errorDetails = document.createElement('div')
    errorDetails.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50'
    errorDetails.innerHTML = `
      <div class="bg-white rounded-lg p-6 max-w-2xl max-h-96 overflow-y-auto">
        <h3 class="text-lg font-medium text-red-900 mb-4">Some records failed to process</h3>
        <ul class="text-sm text-red-700 space-y-1">
          ${errors.map(error => `<li>• ${error}</li>`).join('')}
        </ul>
        <div class="mt-4 flex justify-end">
          <button class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700" onclick="this.closest('.fixed').remove()">
            Close
          </button>
        </div>
      </div>
    `
    
    document.body.appendChild(errorDetails)
  }
}
