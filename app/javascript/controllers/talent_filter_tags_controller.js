import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="talent-filter-tags"
export default class extends Controller {
  static targets = ["container"];

  removeFilter(event) {
    event.preventDefault();

    const filterType = event.currentTarget.dataset.filterType;
    const filterValue = event.currentTarget.dataset.filterValue;

    // Get current URL parameters
    const url = new URL(window.location);
    const params = new URLSearchParams(url.search);

    // Handle different filter types
    if (filterType === "bookmarked") {
      // Remove bookmarked parameter
      params.delete("bookmarked");
    } else if (filterType === "sent_requests") {
      // Remove sent_requests parameter
      params.delete("sent_requests");
    } else if (filterType === "not_contacted") {
      // Remove not_contacted parameter
      params.delete("not_contacted");
    } else if (filterType === "niches") {
      // Handle comma-separated values
      const currentNiches = params.get("niches");
      if (currentNiches) {
        const nichesArray = currentNiches.split(",");
        const updatedNiches = nichesArray.filter(
          (niche) => niche !== filterValue
        );

        if (updatedNiches.length > 0) {
          params.set("niches", updatedNiches.join(","));
        } else {
          params.delete("niches");
        }
      }
    } else if (filterType === "ghostwriter_type") {
      // Handle comma-separated values
      const currentTypes = params.get("ghostwriter_type");
      if (currentTypes) {
        const typesArray = currentTypes.split(",");
        const updatedTypes = typesArray.filter((type) => type !== filterValue);

        if (updatedTypes.length > 0) {
          params.set("ghostwriter_type", updatedTypes.join(","));
        } else {
          params.delete("ghostwriter_type");
        }
      }
    } else if (filterType === "availability_status") {
      // Handle comma-separated values
      const currentStatuses = params.get("availability_status");
      if (currentStatuses) {
        const statusesArray = currentStatuses.split(",");
        const updatedStatuses = statusesArray.filter(
          (status) => status !== filterValue
        );

        if (updatedStatuses.length > 0) {
          params.set("availability_status", updatedStatuses.join(","));
        } else {
          params.delete("availability_status");
        }
      }
    } else if (filterType === "skills") {
      // Handle comma-separated values
      const currentSkills = params.get("skills");
      if (currentSkills) {
        const skillsArray = currentSkills.split(",");
        const updatedSkills = skillsArray.filter(
          (skill) => skill !== filterValue
        );

        if (updatedSkills.length > 0) {
          params.set("skills", updatedSkills.join(","));
        } else {
          params.delete("skills");
        }
      }
    } else if (filterType === "location_preference") {
      // Handle comma-separated values
      const currentLocations = params.get("location_preference");
      if (currentLocations) {
        const locationsArray = currentLocations.split(",");
        const updatedLocations = locationsArray.filter(
          (location) => location !== filterValue
        );

        if (updatedLocations.length > 0) {
          params.set("location_preference", updatedLocations.join(","));
        } else {
          params.delete("location_preference");
        }
      }
    } else if (filterType === "pricing_model") {
      // Handle comma-separated values
      const currentModels = params.get("pricing_model");
      if (currentModels) {
        const modelsArray = currentModels.split(",");
        const updatedModels = modelsArray.filter(
          (model) => model !== filterValue
        );

        if (updatedModels.length > 0) {
          params.set("pricing_model", updatedModels.join(","));
        } else {
          params.delete("pricing_model");
        }
      }
    } else if (filterType === "location") {
      // Handle comma-separated values
      const currentLocations = params.get("location");
      if (currentLocations) {
        const locationsArray = currentLocations.split(",");
        const updatedLocations = locationsArray.filter(
          (location) => location !== filterValue
        );

        if (updatedLocations.length > 0) {
          params.set("location", updatedLocations.join(","));
        } else {
          params.delete("location");
        }
      }
    } else if (filterType === "is_agency") {
      // Remove is_agency parameter
      params.delete("is_agency");
    }

    // Reset to first page when filters change
    params.delete("page");

    // Navigate to the updated URL
    const newUrl = `${url.pathname}?${params.toString()}`;
    window.location.href = newUrl;
  }

  clearAllFilters(event) {
    event.preventDefault();

    // Get current URL without any filter parameters
    const url = new URL(window.location);
    const params = new URLSearchParams();

    // Keep only non-filter parameters if any (like search query)
    const currentParams = new URLSearchParams(url.search);
    if (currentParams.get("query")) {
      params.set("query", currentParams.get("query"));
    }

    // Remove all filter parameters
    const filterParams = [
      "bookmarked",
      "sent_requests",
      "not_contacted",
      "niches",
      "ghostwriter_type",
      "availability_status",
      "skills",
      "location_preference",
      "pricing_model",
      "location",
      "is_agency",
    ];

    filterParams.forEach((param) => {
      params.delete(param);
    });

    // Navigate to the clean URL
    const newUrl = `${url.pathname}?${params.toString()}`;
    window.location.href = newUrl;
  }
}
