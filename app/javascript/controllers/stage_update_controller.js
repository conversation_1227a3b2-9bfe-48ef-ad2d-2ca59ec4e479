import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static values = {
    id: Number,
    status: String,
    url: String,
  };

  connect() {
    // Store available statuses for use in the modal
    this.availableStatuses = [
      "applied",
      "reviewed",
      "interviewing",
      "offered",
      "hired",
      "rejected",
    ];
  }

  openModal(event) {
    event.preventDefault();
    event.stopPropagation();

    // Get the application ID and current status
    const applicationId = this.idValue;
    const currentStatus = this.statusValue;

    // Fetch the modal content
    fetch(
      `/scout/applicants/${applicationId}/stage_change_form?current_status=${currentStatus}`,
      {
        headers: {
          Accept: "text/html, application/xhtml+xml",
        },
      }
    )
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text();
      })
      .then((html) => {
        // Update modal content
        const modalContent = document.getElementById("message-modal-content");
        if (modalContent) {
          modalContent.innerHTML = html;

          // Show modal by dispatching an event to modal controller
          const modalController = document.querySelector(
            '[data-controller="modal"]'
          );
          if (modalController) {
            modalController.dispatchEvent(new CustomEvent("show-modal"));
          } else {
            console.error("Modal controller element not found");
          }
        } else {
          console.error("Modal content element not found");
        }
      })
      .catch((error) => {
        console.error("Error loading stage change modal:", error);
      });
  }

  updateStage(event) {
    // Immediately stop form submission and close modal
    event.preventDefault();
    event.stopPropagation();

    // Force document.body scroll restoration
    document.body.style.overflow = "";

    // Close modal - try multiple methods to ensure it closes
    // Method 1: Direct call on modal controller instance
    const modalController = document.querySelector('[data-controller="modal"]');
    if (modalController) {
      // Try to get the controller instance
      if (
        modalController.__stimulusRef &&
        modalController.__stimulusRef.controller
      ) {
        modalController.__stimulusRef.controller.close();
      }

      // Method 2: Dispatch event
      modalController.dispatchEvent(new CustomEvent("modal-close"));
    }

    // Method 3: Direct DOM manipulation
    const modalElements = document.querySelectorAll(".fixed.inset-0");
    modalElements.forEach((el) => {
      el.classList.add("hidden");
    });

    // Get form data
    const form = event.target;
    const formData = new FormData(form);
    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

    // Submit form after a small delay to allow modal to close first
    setTimeout(() => {
      fetch(this.urlValue, {
        method: "PATCH",
        headers: {
          "X-CSRF-Token": csrfToken,
          Accept: "text/vnd.turbo-stream.html",
        },
        body: formData,
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error("Network response was not ok");
          }
          return response.text();
        })
        .then((html) => {
          // Process any Turbo Stream response
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, "text/html");
          const turboStreams = doc.querySelectorAll("turbo-stream");

          if (turboStreams.length > 0) {
            // Let Turbo process the streams
            document.body.appendChild(doc);
          } else {
            // If no turbo streams, reload the page
            window.location.reload();
          }
        })
        .catch((error) => {
          console.error("Error:", error);
        });
    }, 100);
  }
}
