import { Controller } from "@hotwired/stimulus";
import { Turbo } from "@hotwired/turbo-rails";

export default class extends Controller {
  static targets = ["container"];
  static values = {
    detailsUrl: String,
  };

  connect() {
    // Initialize any necessary setup when controller connects
    console.log("Candidate details controller connected");
  }

  // Load candidate details when a search result is clicked
  loadDetails(event) {
    event.preventDefault();
    const candidateId = event.currentTarget.dataset.candidateId;

    Turbo.visit(`/scout/applicants/${candidateId}`, {
      action: "replace",
      frame: "candidate-details-container",
    });
  }

  close(event) {
    event.preventDefault();
    console.log("Close button clicked");

    // Use turbo-stream fetch instead of Turbo.visit
    fetch("/scout/applicants/placeholder", {
      headers: {
        Accept: "text/vnd.turbo-stream.html",
        "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]')
          .content,
      },
    })
      .then((response) => response.text())
      .then((html) => Turbo.renderStreamMessage(html))
      .catch(console.error);

    // Remove highlight from selected candidate
    document.querySelectorAll(".candidate-selected").forEach((el) => {
      el.classList.remove("candidate-selected");
      el.classList.add("border-stone-200");
      el.classList.remove("border-[#6100FF]", "bg-purple-50");
    });
  }

  // Open message modal
  openMessageModal(event) {
    event.preventDefault();
    event.stopPropagation();

    // Get applicant ID and job ID from data attributes
    const applicantId = event.currentTarget.dataset.applicantId;
    const jobId = event.currentTarget.dataset.jobId;

    // Bail early if we don't have the required IDs
    if (!applicantId || !jobId) {
      console.error("Missing applicant or job ID for message modal");
      return;
    }

    // Construct URL for modal conversation
    const url = `/scout/conversations/modal/${applicantId}?job_id=${jobId}`;

    // Fetch conversation content and update modal
    fetch(url, {
      headers: {
        Accept: "text/html, application/xhtml+xml",
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text();
      })
      .then((html) => {
        // Update modal content
        const modalContent = document.getElementById("message-modal-content");
        if (modalContent) {
          modalContent.innerHTML = html;

          // Show modal by dispatching an event to modal controller
          const modalController = document.querySelector(
            '[data-controller="modal"]'
          );
          if (modalController) {
            modalController.dispatchEvent(new CustomEvent("show-modal"));
          } else {
            console.error("Modal controller element not found");
          }
        } else {
          console.error("Modal content element not found");
        }
      })
      .catch((error) => {
        console.error("Error loading conversation modal:", error);
      });
  }
}
