import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  openModal(event) {
    event.preventDefault();

    // Get talent user ID from the clicked element
    const talentUserId = event.target.closest("[data-talent-user-id]")?.dataset
      .talentUserId;

    if (!talentUserId) {
      console.error("No talent user ID found on clicked element");
      return;
    }

    // Find the chat request modal controller
    const modalElement = document.querySelector(
      '[data-controller*="chat-request-modal"]'
    );

    if (!modalElement) {
      console.error("No chat request modal found on page");
      return;
    }

    // Get the Stimulus controller instance
    const modalController =
      this.application.getControllerForElementAndIdentifier(
        modalElement,
        "chat-request-modal"
      );

    if (modalController) {
      // Set the talent user ID and open the modal
      modalController.talentUserIdValue = talentUserId;
      modalController.showModal();
      modalController.loadModalContent(talentUserId);
    } else {
      console.error("Could not get chat request modal controller instance");
    }
  }
}
