require 'csv'

class SuperAdmin::AdminUsersController < SuperAdmin::AdminBaseController
  include Pagy::Backend
  include SuperAdmin::CsvExportable
  include AdminBulkOperations
  before_action :set_user, only: %i[show edit update]
  before_action :check_edit_permission, only: %i[edit update]

  def index
    @users = User.includes(:roles, :organizations)

    # Apply advanced search if enabled, otherwise use basic search
    if params[:advanced_search] == 'true'
      @users = apply_advanced_search(@users, build_advanced_search_config)
    else
      # Apply basic search
      @users = apply_search(@users, %w[email first_name last_name])

      # Apply basic filters
      @users = @users.where(verified: true) if params[:verified] == 'true'
      @users = @users.where(verified: false) if params[:verified] == 'false'
      @users = @users.where(onboarding_completed: true) if params[
        :onboarding_completed
      ] == 'true'
      @users = @users.where(onboarding_completed: false) if params[
        :onboarding_completed
      ] == 'false'

      # Filter by role
      if params[:role].present? && params[:role] != 'all'
        @users = @users.joins(:roles).where(roles: { name: params[:role] })
      end
    end

    # Apply sorting
    @users =
      apply_sorting(
        @users,
        %w[
          id
          email
          first_name
          last_name
          created_at
          verified
          onboarding_completed
        ],
      )

    respond_to do |format|
      format.html do
        # Handle saved search
        handle_saved_search

        # Paginate for HTML only
        @pagy, @users = pagy(@users, items: @page_size)

        # Set up filter options
        @filter_options = {
          verified: [%w[Verified true], %w[Unverified false]],
          onboarding_completed: [%w[Completed true], %w[Incomplete false]],
          role: Role.pluck(:name).map { |name| [name.humanize, name] },
        }

        # Get saved searches for current user
        @saved_searches = get_saved_searches

        # Set up bulk operations
        @bulk_operations = get_bulk_operations
      end
      format.csv do
        # For CSV, we want all filtered results, not paginated
        @users_for_export = @users
        send_csv_data
      end
      format.json do
        # For JSON, return search results for role assignment
        render json: {
                 users:
                   @users
                     .limit(20)
                     .map do |user|
                       {
                         id: user.id,
                         full_name: user.full_name,
                         email: user.email,
                         initials: user.initials,
                         admin_roles: user.roles.pluck(:name),
                       }
                     end,
               }
      end
    end
  end

  def show
    @organizations = @user.organizations.includes(:jobs)
    @recent_jobs = @user.organizations.joins(:jobs).includes(:jobs).limit(5)
    @chat_requests_sent = @user.sent_chat_requests.includes(:talent).limit(5)
    @chat_requests_received =
      @user.received_chat_requests.includes(:scout).limit(5)
  end

  def edit; end

  def update
    if @user.update(user_params)
      redirect_to super_admin_admin_user_path(@user),
                  notice: 'User was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  protected

  def resource_name
    'users'
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def collection_for_export
    @users_for_export || @users
  end

  def user_params
    params
      .require(:user)
      .permit(
        :email,
        :first_name,
        :last_name,
        :verified,
        :onboarding_completed,
        :scout_signup_completed,
        :talent_signup_completed,
        :signup_intent,
        :onboarding_step,
        :time_zone,
      )
  end

  def build_advanced_search_config
    {
      text_fields: [
        'users.email',
        'users.first_name',
        'users.last_name',
        { table: 'organizations', field: 'name' }
      ],
      date_fields: {
        created: 'users.created_at',
        updated: 'users.updated_at',
        last_sign_in: 'users.last_sign_in_at'
      },
      boolean_fields: %w[verified onboarding_completed],
      association_filters: {
        role: {
          type: :has_many_through,
          association: :roles,
          through_table: 'roles',
          target_key: 'name'
        },
        organization: {
          type: :belongs_to,
          foreign_key: 'organization_id'
        }
      },
      custom_filters: {
        has_role: ->(collection, value) {
          case value
          when 'true'
            collection.joins(:roles).distinct
          when 'false'
            collection.left_joins(:roles).where(roles: { id: nil })
          else
            collection
          end
        }
      }
    }
  end

  # Bulk operations configuration
  def bulk_update_params
    params.permit(:verified, :onboarding_completed, :scout_signup_completed, :talent_signup_completed, :signup_intent, :onboarding_step)
  end

  def get_bulk_update_operations
    [
      {
        key: 'verify_users',
        label: 'Verify Selected',
        action: 'bulk_update',
        params: { verified: true },
        confirm: true,
        confirm_message: 'Are you sure you want to verify the selected users?',
        class: 'bg-green-600 hover:bg-green-700'
      },
      {
        key: 'unverify_users',
        label: 'Unverify Selected',
        action: 'bulk_update',
        params: { verified: false },
        confirm: true,
        confirm_message: 'Are you sure you want to unverify the selected users?',
        class: 'bg-yellow-600 hover:bg-yellow-700'
      },
      {
        key: 'complete_onboarding',
        label: 'Complete Onboarding',
        action: 'bulk_update',
        params: { onboarding_completed: true },
        confirm: true,
        confirm_message: 'Are you sure you want to mark onboarding as complete for the selected users?',
        class: 'bg-blue-600 hover:bg-blue-700'
      }
    ]
  end

  def get_bulk_status_operations
    # Users don't have a status enum, so return empty array
    []
  end

  def get_model_class
    User
  end
end
