require 'csv'

class SuperAdmin::AdminUsersController < SuperAdmin::AdminBaseController
  include Pagy::Backend
  include SuperAdmin::CsvExportable
  before_action :set_user, only: %i[show edit update]
  before_action :check_edit_permission, only: %i[edit update]

  def index
    @users = User.includes(:roles, :organizations)

    # Apply search
    @users = apply_search(@users, %w[email first_name last_name])

    # Apply filters
    @users = @users.where(verified: true) if params[:verified] == 'true'
    @users = @users.where(verified: false) if params[:verified] == 'false'
    @users = @users.where(onboarding_completed: true) if params[
      :onboarding_completed
    ] == 'true'
    @users = @users.where(onboarding_completed: false) if params[
      :onboarding_completed
    ] == 'false'

    # Filter by role
    if params[:role].present?
      @users = @users.joins(:roles).where(roles: { name: params[:role] })
    end

    # Apply sorting
    @users =
      apply_sorting(
        @users,
        %w[
          id
          email
          first_name
          last_name
          created_at
          verified
          onboarding_completed
        ],
      )

    respond_to do |format|
      format.html do
        # Paginate for HTML only
        @pagy, @users = pagy(@users, items: @page_size)

        # Set up filter options
        @filter_options = {
          verified: [%w[Verified true], %w[Unverified false]],
          onboarding_completed: [%w[Completed true], %w[Incomplete false]],
          role: Role.pluck(:name).map { |name| [name.humanize, name] },
        }
      end
      format.csv do
        # For CSV, we want all filtered results, not paginated
        @users_for_export = @users
        send_csv_data
      end
    end
  end

  def show
    @organizations = @user.organizations.includes(:jobs)
    @recent_jobs = @user.organizations.joins(:jobs).includes(:jobs).limit(5)
    @chat_requests_sent = @user.sent_chat_requests.includes(:talent).limit(5)
    @chat_requests_received =
      @user.received_chat_requests.includes(:scout).limit(5)
  end

  def edit; end

  def update
    if @user.update(user_params)
      redirect_to super_admin_admin_user_path(@user),
                  notice: 'User was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  protected

  def resource_name
    'users'
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def collection_for_export
    @users_for_export || @users
  end

  def user_params
    params
      .require(:user)
      .permit(
        :email,
        :first_name,
        :last_name,
        :verified,
        :onboarding_completed,
        :scout_signup_completed,
        :talent_signup_completed,
        :signup_intent,
        :onboarding_step,
        :time_zone,
      )
  end
end
