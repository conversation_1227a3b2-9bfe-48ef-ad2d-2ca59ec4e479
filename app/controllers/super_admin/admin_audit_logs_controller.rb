module SuperAdmin
  class AdminAuditLogsController < SuperAdmin::AdminBaseController
    include SuperAdmin::CsvExportable
    before_action :require_superadmin
    before_action :set_audit_log, only: [:show]

    def index
      @q = params[:q]
      @action_filter = params[:action_filter]
      @admin_filter = params[:admin_filter]
      @resource_type_filter = params[:resource_type_filter]
      @date_range = params[:date_range]
      @sort_by = params[:sort_by] || 'created_at'
      @sort_direction = params[:sort_direction] || 'desc'

      # Start with all audit logs
      logs = AdminAuditLog.includes(:admin_user, :resource)

      # Apply search filter
      if @q.present?
        logs = logs.joins(:admin_user).where(
          "admin_audit_logs.action ILIKE ? OR admin_audit_logs.controller ILIKE ? OR users.email ILIKE ? OR users.first_name ILIKE ? OR users.last_name ILIKE ?",
          "%#{@q}%", "%#{@q}%", "%#{@q}%", "%#{@q}%", "%#{@q}%"
        )
      end

      # Apply action filter
      if @action_filter.present? && @action_filter != 'all'
        logs = logs.where(action: @action_filter)
      end

      # Apply admin filter
      if @admin_filter.present? && @admin_filter != 'all'
        logs = logs.where(admin_user_id: @admin_filter)
      end

      # Apply resource type filter
      if @resource_type_filter.present? && @resource_type_filter != 'all'
        logs = logs.where(resource_type: @resource_type_filter)
      end

      # Apply date range filter
      if @date_range.present?
        case @date_range
        when 'today'
          logs = logs.where('created_at >= ?', Date.current.beginning_of_day)
        when 'yesterday'
          logs = logs.where('created_at >= ? AND created_at < ?', 
                           Date.current.yesterday.beginning_of_day,
                           Date.current.beginning_of_day)
        when 'this_week'
          logs = logs.where('created_at >= ?', Date.current.beginning_of_week)
        when 'last_week'
          logs = logs.where('created_at >= ? AND created_at < ?',
                           Date.current.last_week.beginning_of_week,
                           Date.current.beginning_of_week)
        when 'this_month'
          logs = logs.where('created_at >= ?', Date.current.beginning_of_month)
        when 'last_month'
          logs = logs.where('created_at >= ? AND created_at < ?',
                           Date.current.last_month.beginning_of_month,
                           Date.current.beginning_of_month)
        end
      end

      # Apply sorting
      case @sort_by
      when 'admin'
        logs = logs.joins(:admin_user).order("users.first_name #{@sort_direction}, users.last_name #{@sort_direction}")
      when 'action'
        logs = logs.order("action #{@sort_direction}")
      when 'resource_type'
        logs = logs.order("resource_type #{@sort_direction}")
      when 'created_at'
        logs = logs.order("created_at #{@sort_direction}")
      else
        logs = logs.order("created_at #{@sort_direction}")
      end

      # Get filter options
      @available_actions = AdminAuditLog.distinct.pluck(:action).compact.sort
      @available_admins = User.joins(:admin_audit_logs).distinct.select(:id, :first_name, :last_name, :email).order(:first_name, :last_name)
      @available_resource_types = AdminAuditLog.distinct.pluck(:resource_type).compact.sort

      # Paginate results
      @pagy, @audit_logs = pagy(logs, limit: 50)

      respond_to do |format|
        format.html
        format.csv do
          # For CSV export, get all filtered results without pagination
          @audit_logs_for_export = logs.limit(10000) # Reasonable limit for CSV
          send_csv_data
        end
      end
    end

    def show
      @changes = @audit_log.formatted_changes
    end

    protected

    def resource_name
      'audit_logs'
    end

    private

    def set_audit_log
      @audit_log = AdminAuditLog.find(params[:id])
    end

    def collection_for_export
      @audit_logs_for_export || @audit_logs
    end

    def send_csv_data
      csv_data = generate_csv(@audit_logs_for_export)
      filename = "admin_audit_logs_#{Date.current.strftime('%Y%m%d')}.csv"
      
      send_data csv_data,
                filename: filename,
                type: 'text/csv',
                disposition: 'attachment'
    end

    def generate_csv(logs)
      CSV.generate(headers: true) do |csv|
        # CSV headers
        csv << [
          'ID',
          'Date/Time',
          'Admin User',
          'Admin Email',
          'Action',
          'Controller',
          'Resource Type',
          'Resource ID',
          'Resource Name',
          'Description',
          'IP Address',
          'User Agent',
          'Changes'
        ]

        # CSV rows
        logs.find_each do |log|
          csv << [
            log.id,
            log.created_at.strftime('%Y-%m-%d %H:%M:%S %Z'),
            log.admin_name,
            log.admin_email,
            log.action.humanize,
            log.controller,
            log.resource_type || 'N/A',
            log.resource_id || 'N/A',
            log.resource_identifier,
            log.description,
            log.ip_address || 'N/A',
            log.user_agent || 'N/A',
            log.changes.present? ? log.changes.to_json : 'N/A'
          ]
        end
      end
    end
  end
end
