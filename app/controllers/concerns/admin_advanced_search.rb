module AdminAdvancedSearch
  extend ActiveSupport::Concern

  included do
    before_action :set_advanced_search_params, only: [:index]
  end

  private

  def set_advanced_search_params
    @advanced_search = params[:advanced_search] == 'true'
    @saved_search_id = params[:saved_search_id]
    @search_name = params[:search_name]
    
    # Load saved search if specified
    if @saved_search_id.present? && Current.user
      @saved_search = Current.user.saved_searches.find_by(id: @saved_search_id)
      if @saved_search
        # Merge saved search params with current params
        saved_params = JSON.parse(@saved_search.search_params)
        saved_params.each do |key, value|
          params[key] = value unless params[key].present?
        end
      end
    end
  end

  def apply_advanced_search(collection, config = {})
    return collection unless @advanced_search

    # Text search across multiple fields
    if params[:text_search].present?
      collection = apply_text_search(collection, params[:text_search], config[:text_fields] || [])
    end

    # Date range filters
    collection = apply_date_filters(collection, config[:date_fields] || {})

    # Numeric range filters
    collection = apply_numeric_filters(collection, config[:numeric_fields] || {})

    # Boolean filters
    collection = apply_boolean_filters(collection, config[:boolean_fields] || [])

    # Association filters
    collection = apply_association_filters(collection, config[:association_filters] || {})

    # Custom filters
    collection = apply_custom_filters(collection, config[:custom_filters] || {})

    collection
  end

  def apply_text_search(collection, search_term, fields)
    return collection if search_term.blank? || fields.empty?

    search_conditions = []
    search_values = []

    fields.each do |field_config|
      if field_config.is_a?(String)
        # Simple field
        search_conditions << "#{field_config} ILIKE ?"
        search_values << "%#{search_term}%"
      elsif field_config.is_a?(Hash)
        # Association field
        table = field_config[:table]
        field = field_config[:field]
        search_conditions << "#{table}.#{field} ILIKE ?"
        search_values << "%#{search_term}%"
      end
    end

    return collection if search_conditions.empty?

    collection.where(search_conditions.join(' OR '), *search_values)
  end

  def apply_date_filters(collection, date_fields)
    date_fields.each do |field, column|
      # Date range filter
      if params["#{field}_from"].present?
        begin
          from_date = Date.parse(params["#{field}_from"])
          collection = collection.where("#{column} >= ?", from_date.beginning_of_day)
        rescue ArgumentError
          # Invalid date format, skip
        end
      end

      if params["#{field}_to"].present?
        begin
          to_date = Date.parse(params["#{field}_to"])
          collection = collection.where("#{column} <= ?", to_date.end_of_day)
        rescue ArgumentError
          # Invalid date format, skip
        end
      end

      # Predefined date ranges
      if params["#{field}_range"].present?
        collection = apply_predefined_date_range(collection, column, params["#{field}_range"])
      end
    end

    collection
  end

  def apply_predefined_date_range(collection, column, range)
    case range
    when 'today'
      collection.where("#{column} >= ?", Date.current.beginning_of_day)
    when 'yesterday'
      collection.where("#{column} >= ? AND #{column} < ?", 
                      Date.current.yesterday.beginning_of_day,
                      Date.current.beginning_of_day)
    when 'this_week'
      collection.where("#{column} >= ?", Date.current.beginning_of_week)
    when 'last_week'
      collection.where("#{column} >= ? AND #{column} < ?",
                      Date.current.last_week.beginning_of_week,
                      Date.current.beginning_of_week)
    when 'this_month'
      collection.where("#{column} >= ?", Date.current.beginning_of_month)
    when 'last_month'
      collection.where("#{column} >= ? AND #{column} < ?",
                      Date.current.last_month.beginning_of_month,
                      Date.current.beginning_of_month)
    when 'this_year'
      collection.where("#{column} >= ?", Date.current.beginning_of_year)
    when 'last_year'
      collection.where("#{column} >= ? AND #{column} < ?",
                      Date.current.last_year.beginning_of_year,
                      Date.current.beginning_of_year)
    else
      collection
    end
  end

  def apply_numeric_filters(collection, numeric_fields)
    numeric_fields.each do |field, column|
      if params["#{field}_min"].present?
        begin
          min_value = params["#{field}_min"].to_f
          collection = collection.where("#{column} >= ?", min_value)
        rescue ArgumentError
          # Invalid number format, skip
        end
      end

      if params["#{field}_max"].present?
        begin
          max_value = params["#{field}_max"].to_f
          collection = collection.where("#{column} <= ?", max_value)
        rescue ArgumentError
          # Invalid number format, skip
        end
      end
    end

    collection
  end

  def apply_boolean_filters(collection, boolean_fields)
    boolean_fields.each do |field|
      if params[field].present? && params[field] != 'all'
        value = params[field] == 'true'
        collection = collection.where(field => value)
      end
    end

    collection
  end

  def apply_association_filters(collection, association_filters)
    association_filters.each do |filter_name, config|
      param_value = params[filter_name]
      next if param_value.blank? || param_value == 'all'

      case config[:type]
      when :belongs_to
        collection = collection.where("#{config[:foreign_key]}" => param_value)
      when :has_many
        collection = collection.joins(config[:association]).where("#{config[:table]}.id" => param_value)
      when :has_many_through
        collection = collection.joins(config[:association]).where("#{config[:through_table]}.#{config[:target_key]}" => param_value)
      end
    end

    collection
  end

  def apply_custom_filters(collection, custom_filters)
    custom_filters.each do |filter_name, filter_proc|
      param_value = params[filter_name]
      next if param_value.blank?

      collection = filter_proc.call(collection, param_value)
    end

    collection
  end

  def save_search
    return unless Current.user && params[:save_search] == 'true' && params[:search_name].present?

    search_params = extract_search_params
    
    saved_search = Current.user.saved_searches.find_or_initialize_by(
      name: params[:search_name],
      resource_type: controller_name.classify
    )
    
    saved_search.search_params = search_params.to_json
    saved_search.save!

    saved_search
  end

  def extract_search_params
    # Extract all search-related parameters
    search_params = {}
    
    # Basic search params
    search_params[:search] = params[:search] if params[:search].present?
    search_params[:sort_by] = params[:sort_by] if params[:sort_by].present?
    search_params[:sort_direction] = params[:sort_direction] if params[:sort_direction].present?
    
    # Advanced search params
    if @advanced_search
      search_params[:advanced_search] = 'true'
      search_params[:text_search] = params[:text_search] if params[:text_search].present?
      
      # Extract all filter parameters
      params.each do |key, value|
        if key.match?(/_from$|_to$|_range$|_min$|_max$/) || 
           key.in?(%w[status verified onboarding_completed role organization job_category platform budget_range is_premium])
          search_params[key] = value if value.present?
        end
      end
    end

    search_params
  end

  def get_saved_searches
    return [] unless Current.user
    
    Current.user.saved_searches
                .where(resource_type: controller_name.classify)
                .order(:name)
  end

  def build_advanced_search_config
    # Override this method in each controller to define search configuration
    {
      text_fields: [],
      date_fields: {},
      numeric_fields: {},
      boolean_fields: [],
      association_filters: {},
      custom_filters: {}
    }
  end
end
