module SuperAdmin
  module CsvExportable
    extend ActiveSupport::Concern

    private

    def send_csv_data
      csv_data = generate_csv_data
      filename = "#{controller_name}_#{Date.current.strftime('%Y%m%d')}.csv"
      
      send_data csv_data, 
                filename: filename,
                type: 'text/csv',
                disposition: 'attachment'
    end

    def generate_csv_data
      case controller_name
      when 'admin_users'
        generate_users_csv
      when 'admin_jobs'
        generate_jobs_csv
      when 'admin_job_applications'
        generate_job_applications_csv
      when 'admin_messages'
        generate_messages_csv
      when 'admin_talent_profiles'
        generate_talent_profiles_csv
      when 'admin_organizations'
        generate_organizations_csv
      when 'admin_conversations'
        generate_conversations_csv
      when 'admin_chat_requests'
        generate_chat_requests_csv
      else
        raise "CSV export not implemented for #{controller_name}"
      end
    end

    def generate_users_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Name', 'Email', 'Verified', 'Signup Intent', 'Organizations', 'Created At']
        
        collection_for_export.find_each do |user|
          csv << [
            user.id,
            user.full_name,
            user.email,
            user.verified? ? 'Yes' : 'No',
            user.signup_intent&.humanize,
            user.organizations.pluck(:name).join(', '),
            user.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_jobs_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Title', 'Category', 'Status', 'Organization', 'Budget Range', 'Platform', 'Applications Count', 'Created At', 'Published At']

        collection_for_export.find_each do |job|
          csv << [
            job.id,
            job.title,
            job.job_category&.humanize,
            job.status&.humanize,
            job.organization&.name,
            job.budget_range&.humanize&.gsub('_', ' '),
            job.platform&.humanize&.gsub('_', ' '),
            job.job_applications.count,
            job.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            job.published_at&.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_job_applications_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Applicant Name', 'Applicant Email', 'Job Title', 'Status', 'Invitation Status', 'Salary Consideration', 'Applied At']
        
        collection_for_export.find_each do |application|
          csv << [
            application.id,
            application.user.full_name,
            application.user.email,
            application.job.title,
            application.status&.humanize,
            application.job_invitation&.status&.humanize || 'Not Invited',
            application.salary_consideration ? 'Yes' : 'No',
            application.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_messages_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Sender Name', 'Sender Email', 'Conversation ID', 'Message Preview', 'Read Status', 'Created At']
        
        collection_for_export.find_each do |message|
          csv << [
            message.id,
            message.user.full_name,
            message.user.email,
            message.conversation.id,
            message.body.truncate(100),
            message.read_at ? 'Read' : 'Unread',
            message.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_talent_profiles_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Name', 'Email', 'Headline', 'Location', 'Availability', 'Pricing Model', 'Price Min', 'Price Max', 'Is Agency', 'Is Premium', 'Created At']
        
        collection_for_export.find_each do |profile|
          csv << [
            profile.id,
            profile.user.full_name,
            profile.user.email,
            profile.headline,
            profile.location,
            profile.availability_status&.humanize,
            profile.pricing_model&.humanize,
            profile.price_range_min,
            profile.price_range_max,
            profile.is_agency ? 'Yes' : 'No',
            profile.is_premium ? 'Yes' : 'No',
            profile.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_organizations_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Name', 'Company Size', 'Members Count', 'Jobs Count', 'Created At']
        
        collection_for_export.find_each do |org|
          csv << [
            org.id,
            org.name,
            org.size&.humanize,
            org.users.count,
            org.jobs.count,
            org.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_conversations_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Participants', 'Messages Count', 'Related Job', 'Last Activity', 'Created At']
        
        collection_for_export.find_each do |conversation|
          csv << [
            conversation.id,
            conversation.users.pluck(:email).join(', '),
            conversation.messages.count,
            conversation.job&.title || 'No Job',
            conversation.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
            conversation.created_at.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def generate_chat_requests_csv
      CSV.generate(headers: true) do |csv|
        csv << ['ID', 'Scout Name', 'Scout Email', 'Talent Name', 'Talent Email', 'Status', 'Note', 'Requested At']
        
        collection_for_export.find_each do |request|
          csv << [
            request.id,
            request.scout.full_name,
            request.scout.email,
            request.talent.full_name,
            request.talent.email,
            request.status&.humanize,
            request.note,
            request.requested_at&.strftime('%Y-%m-%d %H:%M:%S')
          ]
        end
      end
    end

    def collection_for_export
      # This should be overridden in each controller to return the filtered collection
      # For now, return the instance variable that matches the controller name
      instance_variable_get("@#{controller_name.sub('admin_', '')}")
    end
  end
end
