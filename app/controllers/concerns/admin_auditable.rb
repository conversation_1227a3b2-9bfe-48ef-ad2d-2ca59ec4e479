module AdminAuditable
  extend ActiveSupport::Concern

  included do
    after_action :log_admin_action, if: :should_log_action?
  end

  private

  def log_admin_action
    return unless Current.user&.admin?
    return if request.get? && !should_log_read_action?

    action_name = determine_action_name
    resource = determine_resource
    changes = determine_changes(resource)

    AdminAuditLog.log_action(
      action: action_name,
      controller: controller_path,
      resource: resource,
      changes: changes,
      admin_user: Current.user
    )
  end

  def should_log_action?
    # Only log actions in admin controllers
    controller_path.start_with?('super_admin/') && 
    # Skip certain actions that don't need logging
    !%w[new edit].include?(action_name) &&
    # Skip if user is not an admin
    Current.user&.admin?
  end

  def should_log_read_action?
    # Log read actions for sensitive resources
    %w[show index].include?(action_name) && 
    %w[admin_users admin_roles admin_audit_logs].include?(controller_name)
  end

  def determine_action_name
    case action_name
    when 'create'
      'create'
    when 'update'
      'update'
    when 'destroy'
      'delete'
    when 'show'
      'read'
    when 'index'
      if params[:format] == 'csv'
        'export'
      elsif params[:search].present? || params[:filter].present?
        'search'
      else
        'read'
      end
    when 'assign_role', 'remove_role'
      action_name
    when 'impersonate'
      'impersonate'
    when 'bulk_update'
      'bulk_update'
    when 'bulk_delete'
      'bulk_delete'
    when 'reset_password'
      'password_reset'
    when 'activate'
      'account_activate'
    when 'deactivate'
      'account_deactivate'
    else
      action_name
    end
  end

  def determine_resource
    # Try to find the resource based on common patterns
    if instance_variable_defined?("@#{controller_name.singularize}")
      instance_variable_get("@#{controller_name.singularize}")
    elsif params[:id].present?
      # Try to find the resource by ID
      resource_class = infer_resource_class
      resource_class&.find_by(id: params[:id])
    else
      nil
    end
  end

  def determine_changes(resource)
    return nil unless resource.respond_to?(:previous_changes)
    return nil if resource.previous_changes.empty?

    # Filter out sensitive or irrelevant changes
    filtered_changes = resource.previous_changes.except(
      'updated_at', 
      'created_at', 
      'password_digest',
      'remember_token'
    )

    filtered_changes.present? ? filtered_changes : nil
  end

  def infer_resource_class
    # Convert controller name to model class
    case controller_name
    when 'admin_users'
      User
    when 'admin_jobs'
      Job
    when 'admin_organizations'
      Organization
    when 'admin_roles'
      Role
    when 'admin_chat_requests'
      ChatRequest
    when 'admin_conversations'
      Conversation
    when 'admin_messages'
      Message
    when 'admin_job_applications'
      JobApplication
    when 'admin_talent_profiles'
      TalentProfile
    else
      # Try to infer from controller name
      begin
        controller_name.sub('admin_', '').singularize.camelize.constantize
      rescue NameError
        nil
      end
    end
  end

  # Helper methods for specific actions
  def log_role_assignment(user, role, action_type)
    AdminAuditLog.log_action(
      action: action_type == :assign ? 'assign_role' : 'remove_role',
      controller: controller_path,
      resource: user,
      changes: { 'role' => [nil, role.name] },
      admin_user: Current.user
    )
  end

  def log_bulk_action(action_type, resource_type, count, details = {})
    AdminAuditLog.log_action(
      action: "bulk_#{action_type}",
      controller: controller_path,
      resource: nil,
      changes: {
        'resource_type' => [nil, resource_type],
        'count' => [nil, count],
        'details' => [nil, details]
      },
      admin_user: Current.user
    )
  end

  def log_export_action(resource_type, filters = {})
    AdminAuditLog.log_action(
      action: 'export',
      controller: controller_path,
      resource: nil,
      changes: {
        'resource_type' => [nil, resource_type],
        'filters' => [nil, filters],
        'format' => [nil, params[:format] || 'csv']
      },
      admin_user: Current.user
    )
  end

  def log_impersonation_start(target_user)
    AdminAuditLog.log_action(
      action: 'impersonate',
      controller: 'super_admin/masquerades',
      resource: target_user,
      changes: {
        'impersonation_started' => [false, true],
        'target_user' => [nil, target_user.email]
      },
      admin_user: Current.user
    )
  end
end
