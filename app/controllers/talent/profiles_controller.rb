module Talent
  class ProfilesController < Talent::BaseController
    before_action :set_profile, only: %i[edit show]

    def show
      redirect_to edit_talent_profile_path if @profile.nil?
    end

    def edit
      @profile ||= Current.user.build_talent_profile
    end

    def create
      @profile = Current.user.build_talent_profile
      if @profile.update(profile_params)
        redirect_to(talent_profile_path, notice: 'Profile created successfully')
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def update
      @profile =
        Current.user.talent_profile || Current.user.build_talent_profile

      # Process the params before updating the profile
      processed_params = process_profile_params(profile_params)

      if @profile.update(processed_params)
        # Mark talent signup as complete upon successful profile save/update
        Current.user.update(talent_signup_completed: true)

        redirect_to(
          talent_profile_path,
          notice:
            if @profile.previously_new_record?
              'Profile created successfully'
            else
              'Profile updated successfully'
            end,
        )
      else
        render :edit, status: :unprocessable_entity
      end
    end

    private

    def set_profile
      @profile = Current.user.talent_profile
    end

    def profile_params
      params
        .require(:talent_profile)
        .permit(
          :bio,
          :looking_for,
          :about,
          :vsl_link,
          :availability_status,
          :price_range_min,
          :price_range_max,
          :pricing_model,
          :portfolio_link,
          :linkedin_url,
          :x_url,
          :website_url,
          :platform_choice,
          :location,
          :instagram_url,
          :threads_url,
          :is_agency,
          :location_preference,
          :headline,
          :skills,
          :niches,
          skills: [],
          niches: [],
          outcomes: [],
          ghostwriter_type: [],
          social_media_specialty: [],
          achievement_badges: [],
        )
    end

    def process_profile_params(params)
      # Convert string skills/niches to arrays if needed
      params[:skills] = params[:skills].to_s.split(',') if params[:skills]
        .is_a?(String)
      params[:niches] = params[:niches].to_s.split(',') if params[:niches]
        .is_a?(String)
      params
    end
  end
end
