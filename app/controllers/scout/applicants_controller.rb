module Scout
  class ApplicantsController < Scout::BaseController
    before_action :set_applications, only: [:index]

    def index
      # First get distinct job IDs with applications
      job_ids =
        Job
          .where(organization_id: Current.user.organizations.pluck(:id))
          .joins(:job_applications)
          .distinct
          .pluck(:id)

      # Then fetch the jobs with titles in correct order
      @jobs = Job.where(id: job_ids).order(:title)
      @job_ids = @jobs.pluck(:id)

      # Build search conditions
      search_conditions = {}

      # Add job filter if provided
      if params[:job_id].present?
        search_conditions[:job_id] = params[:job_id]
      else
        search_conditions[:job_id] = @job_ids
      end

      # Add status filter if provided
      search_conditions[:status] = params[:status] if params[:status].present?

      # Execute search with conditions
      if params[:query].present?
        @applications =
          JobApplication.search(
            params[:query],
            where: search_conditions,
            order: {
              updated_at: :desc,
            },
            page: params[:page],
            per_page: 20,
          )
      else
        @applications =
          JobApplication.search(
            '*',
            where: search_conditions,
            order: {
              updated_at: :desc,
            },
            aggs: {
              'status' => {
                limit: 20,
              },
              'job_id' => {
                limit: 20,
              },
            },
            page: params[:page],
            per_page: 20,
          )
      end

      @applications = @applications.results
      @aggregations = @applications.aggs if @applications.respond_to?(:aggs)

      # Filter out empty buckets from aggregations
      if @aggregations
        @aggregations.each do |agg_name, agg_data|
          if agg_data['buckets']
            agg_data['buckets'] =
              agg_data['buckets'].reject { |bucket| bucket['doc_count'] == 0 }
          end
        end
      end

      # Add nested grouping by job and then by status
      @job_grouped_applications =
        @applications
          .group_by(&:job)
          .transform_values { |job_apps| job_apps.group_by(&:status) }
    end

    # Add this method to your Scout::ApplicantsController
    def show
      @application = JobApplication.find(params[:id])

      respond_to do |format|
        format.html do
          render partial: 'candidate_details',
                 locals: {
                   application: @application,
                 }
        end
        format.json { render json: @application }
      end
    end

    def update
      @application = JobApplication.find(params[:id])
      @old_status = @application.status

      if @application.update(application_params)
        respond_to do |format|
          format.turbo_stream do
            # Set up the variables needed for the partials
            set_applications_and_stats_for_partial # Renamed and will modify this method

            render turbo_stream: [
                     turbo_stream.replace(
                       'applicants-list',
                       partial: 'applicant_results',
                     ),
                     turbo_stream.replace(
                       'application_stats_frame', # Target the new frame
                       partial: 'application_stats', # Render the new partial
                     ),
                     turbo_stream.replace(
                       # Add this stream for candidate details
                       'candidate-details-container',
                       partial: 'candidate_details',
                       locals: {
                         application: @application,
                       },
                     ),
                   ]
          end
          format.json { render json: { status: :ok } }
          format.html { redirect_to scout_applicants_path }
        end
      else
        respond_to do |format|
          format.turbo_stream do
            render turbo_stream: [
                     turbo_stream.replace(
                       'modal_actions',
                       partial: 'shared/form_errors',
                       locals: {
                         errors: @application.errors,
                       },
                     ),
                   ]
          end
          format.json do
            render json: {
                     errors: @application.errors,
                   },
                   status: :unprocessable_entity
          end
          format.html do
            redirect_to scout_applicants_path,
                        alert: 'Could not update application status.'
          end
        end
      end
    end

    # Add this method to your controller
    def placeholder
      respond_to do |format|
        format.turbo_stream do
          render turbo_stream:
                   turbo_stream.replace(
                     'candidate-details-container',
                     partial: 'placeholder',
                   )
        end
      end
    end

    # Add this method to your existing ApplicantsController
    # Add this method to your ApplicantsController
    def details
      @application = JobApplication.find(params[:id])

      respond_to do |format|
        format.turbo_stream
        format.html do
          render partial: 'candidate_details',
                 locals: {
                   application: @application,
                 }
        end
      end
    end

    # Handle the stage change form display
    def stage_change_form
      @application = JobApplication.find(params[:id])

      respond_to do |format|
        format.html do
          render partial: 'stage_change_form',
                 locals: {
                   application: @application,
                 }
        end
      end
    end

    private

    def application_params
      params.require(:job_application).permit(:status)
    end

    # Removed duplicate private keyword and moved placeholder method above it

    def set_applications
      @applications =
        if params[:job_id].present?
          JobApplication.where(job_id: params[:job_id])
        else
          JobApplication.all
        end

      @grouped_applications = @applications.group_by(&:status)
    end

    def set_applications_and_stats_for_partial # Renamed method
      # First get distinct job IDs with applications
      job_ids =
        Job
          .where(organization_id: Current.user.organizations.pluck(:id))
          .joins(:job_applications)
          .distinct
          .pluck(:id)

      # Then fetch the jobs with titles in correct order
      @jobs = Job.where(id: job_ids).order(:title)
      @job_ids = @jobs.pluck(:id)

      # Apply the same job filter if it was present in the request
      if params[:job_id].present?
        @applications = JobApplication.where(job_id: params[:job_id])
      else
        @applications = JobApplication.where(job_id: @job_ids)
      end

      # Apply status filter if it was present
      @applications = @applications.where(status: params[:status]) if params[
        :status
      ].present?

      # Sort by updated_at (similar to the search ordering)
      @applications = @applications.order(updated_at: :desc)

      # Calculate stats needed for the _application_stats partial
      # This logic is similar to what's in index.html.erb
      # Ensure @job_ids is set correctly by the preceding logic in this method.
      # If params[:job_id] was used to filter @applications, it should also be used for stats.
      current_job_filter = params[:job_id].present? ? params[:job_id] : @job_ids

      @stats_total_counts =
        JobApplication.where(job_id: current_job_filter).group(:status).count
      @total_applications = @stats_total_counts.values.sum
    end
  end
end
