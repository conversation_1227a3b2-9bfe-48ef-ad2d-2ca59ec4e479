<!DOCTYPE html>
<html class="antialiased scrollbar-gutter-stable"> <%# Added scrollbar-gutter-stable %>
  <head>
    <title><%= content_for(:title) || "Ghostwrote" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= action_cable_meta_tag %>
<script>
    !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init bs ws ge fs capture De Ai $s register register_once register_for_session unregister unregister_for_session Is getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty xs Ss createPersonProfile Es gs opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing ys debug ks getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
    posthog.init('phc_vgVSNt8fZ4lCmxFuGhWK98EiWNgggHYrdlMQbp42JCm', {
        api_host: 'https://us.i.posthog.com',
        person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
    })
</script>
    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <%= favicon_link_tag asset_path('favicon.ico') %>

    <%= javascript_pack_tag 'application' %>
    <%= stylesheet_link_tag :app %>

  </head>

  <% if !Current.user&.id %>
    <body class="bg-stone-50">
  <% else %>
    <body data-user-id="<%= Current.user&.id || "" %>" class="min-h-screen bg-stone-50">
  <% end %>
    <div class="flex min-h-screen" data-controller="talent-sidebar">
      <!-- Shadcn-inspired Sidebar -->
      <%= render partial: "talent/shared/sidenav" %>
      
      <!-- Backdrop for mobile sidebar -->
      <div class="fixed inset-0 z-30 hidden bg-black/50 lg:hidden" data-sidebar-target="backdrop" data-action="click->talent-sidebar#hide"></div>
      
      <!-- Main Content -->
      <div class="flex flex-col flex-1 w-full transition-all duration-300 ease-in-out sidebar-expanded-main" data-sidebar-target="main">
        <%= render "shared/impersonation_banner" %>
        <main class="flex-1 py-2 pr-2 overflow-auto">
          <%= yield %>
        </main>
      </div>
    </div>
  </body>
</html>
