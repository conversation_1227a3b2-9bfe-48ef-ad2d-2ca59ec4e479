<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title><%= content_for?(:title) ? yield(:title) + " - " : "" %>Ghostwrote Admin</title>
    <meta name="description" content="<%= content_for?(:description) ? yield(:description) : "Ghostwrote Admin Interface" %>">

    <script>
    !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]);var n=t;if("undefined"!=typeof e)try{n=t[e]}catch(t){return}var p=n;if("function"==typeof p)return function(){var t=Array.prototype.slice.call(arguments);t.unshift(e),p.apply(n,t)}return p}(p=t.createElement("script")).type="text/javascript",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init capture register register_once register_for_session unregister unregister_for_session getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey getNextSurveyStep identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing debug".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
    posthog.init('phc_vgVSNt8fZ4lCmxFuGhWK98EiWNgggHYrdlMQbp42JCm', {
        api_host: 'https://us.i.posthog.com',
        person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
    })
</script>

    <%= favicon_link_tag asset_path('favicon.ico') %>
    <%= stylesheet_link_tag "application" %>
    <%= javascript_pack_tag "application" %>
  </head>

  <body class="bg-stone-50" data-user-id="<%= Current.user&.id || "" %>">
    <main class="bg-stone-50">
      <%= render "shared/super_admin_navbar" %>
      
      <div id="flash-messages" class="container px-4 mx-auto">
        <%= render "shared/flash_messages" %>
      </div>
      
      <div class="flex">
        <!-- Admin Sidebar -->
        <div class="w-64 bg-white shadow-sm border-r border-stone-200 min-h-screen">
          <%= render "shared/admin_sidebar" %>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 p-8">
          <%= yield %>
        </div>
      </div>
    </main>
  </body>
</html>
