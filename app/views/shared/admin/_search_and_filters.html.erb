<%#
  Shared admin search and filters component

  Usage:
  <%= render 'shared/admin/search_and_filters',
      search_placeholder: "Search users...",
      filters: [
        { key: 'status', label: 'Status', options: [['Active', 'active'], ['Inactive', 'inactive']] }
      ]
  %>

<% 
  search_placeholder ||= "Search..."
  filters ||= []
%>

<div class="bg-white p-6 rounded-lg shadow-sm mb-6">
  <%= form_with url: request.path, method: :get, local: true, class: "space-y-4" do |form| %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 xl:grid-cols-8 gap-4 items-end">
      <!-- Search Input -->
      <div class="md:col-span-2 lg:col-span-2 xl:col-span-3">
        <%= form.label :search, "Search", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.text_field :search,
            value: params[:search],
            placeholder: search_placeholder,
            class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" %>
      </div>

      <!-- Filters -->
      <% filters.each do |filter| %>
        <div class="lg:col-span-1">
          <%= form.label filter[:key], filter[:label], class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select filter[:key],
              options_for_select([['All', '']] + filter[:options], params[filter[:key]]),
              {},
              { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" } %>
        </div>
      <% end %>

      <!-- Page Size -->
      <div class="lg:col-span-1">
        <%= form.label :page_size, "Per Page", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :page_size,
            options_for_select([['25', 25], ['50', 50], ['100', 100]], params[:page_size] || 25),
            {},
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" } %>
      </div>

      <!-- Action Buttons -->
      <div class="lg:col-span-1 flex space-x-2">
        <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        <% if params[:search].present? || filters.any? { |f| params[f[:key]].present? } %>
          <%= link_to "Clear", request.path,
              class: "inline-flex items-center px-3 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        <% end %>
      </div>
    </div>
    
    <!-- Preserve sort parameters -->
    <%= form.hidden_field :sort_by, value: params[:sort_by] if params[:sort_by].present? %>
    <%= form.hidden_field :sort_direction, value: params[:sort_direction] if params[:sort_direction].present? %>
  <% end %>
</div>
