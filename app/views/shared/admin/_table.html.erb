<%#
  Shared admin table component

  Usage:
  <%= render 'shared/admin/table',
      collection: @users,
      columns: [
        { key: 'id', label: 'ID', sortable: true },
        { key: 'email', label: 'Email', sortable: true },
        { key: 'created_at', label: 'Created', sortable: true }
      ],
      actions: true,
      show_path: ->(record) { super_admin_admin_user_path(record) },
      edit_path: ->(record) { edit_super_admin_admin_user_path(record) },
      bulk_operations: true,
      bulk_operations_config: @bulk_operations
  %>

<%
  columns ||= []
  actions ||= false
  show_path ||= nil
  edit_path ||= nil
  delete_path ||= nil
  bulk_operations ||= false
  bulk_operations_config ||= []
%>

<% if bulk_operations && bulk_operations_config.any? %>
  <!-- Bulk Operations Toolbar -->
  <div id="bulk-toolbar" class="hidden bg-blue-50 border-b border-blue-200 px-6 py-3">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <span id="selected-count" class="text-sm font-medium text-blue-900">0 selected</span>
        <button type="button" id="clear-selection" class="text-sm text-blue-600 hover:text-blue-800">
          Clear selection
        </button>
      </div>
      <div class="flex items-center space-x-2">
        <% bulk_operations_config.each do |operation| %>
          <button type="button"
                  data-bulk-action="<%= operation[:action] %>"
                  data-operation-key="<%= operation[:key] %>"
                  data-confirm="<%= operation[:confirm] %>"
                  data-confirm-message="<%= operation[:confirm_message] %>"
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white <%= operation[:class] || 'bg-blue-600 hover:bg-blue-700' %> focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <%= operation[:label] %>
          </button>
        <% end %>
      </div>
    </div>
  </div>
<% end %>

<div class="bg-white shadow-sm rounded-lg overflow-hidden"
     data-controller="<%= bulk_operations ? 'bulk-operations' : '' %>"
     data-bulk-operations-base-url-value="<%= request.path %>">
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-stone-200">
      <thead class="bg-stone-50">
        <tr>
          <% if bulk_operations %>
            <th class="px-6 py-3 text-left">
              <input type="checkbox"
                     id="select-all"
                     data-bulk-operations-target="selectAll"
                     class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded">
            </th>
          <% end %>
          <% columns.each do |column| %>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
              <% if column[:sortable] && @sort_by %>
                <%= link_to column[:label], 
                    request.params.merge(
                      sort_by: column[:key], 
                      sort_direction: (@sort_by == column[:key].to_s && @sort_direction == 'asc') ? 'desc' : 'asc'
                    ),
                    class: "flex items-center space-x-1 hover:text-stone-700" do %>
                  <span><%= column[:label] %></span>
                  <% if @sort_by == column[:key].to_s %>
                    <% if @sort_direction == 'asc' %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                      </svg>
                    <% else %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    <% end %>
                  <% end %>
                <% end %>
              <% else %>
                <%= column[:label] %>
              <% end %>
            </th>
          <% end %>
          <% if actions %>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
              Actions
            </th>
          <% end %>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-stone-200">
        <% if collection.any? %>
          <% collection.each do |record| %>
            <tr class="hover:bg-stone-50">
              <% if bulk_operations %>
                <td class="px-6 py-4 whitespace-nowrap">
                  <input type="checkbox"
                         data-bulk-operations-target="recordCheckbox"
                         data-record-id="<%= record.id %>"
                         class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-stone-300 rounded">
                </td>
              <% end %>
              <% columns.each do |column| %>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                  <% if column[:render] %>
                    <%= column[:render].call(record) %>
                  <% else %>
                    <% value = record.send(column[:key]) %>
                    <% if value.is_a?(Time) || value.is_a?(DateTime) %>
                      <%= value.strftime('%b %d, %Y at %I:%M %p') %>
                    <% elsif value.is_a?(Date) %>
                      <%= value.strftime('%b %d, %Y') %>
                    <% elsif value.is_a?(TrueClass) || value.is_a?(FalseClass) %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
                        <%= value ? 'Yes' : 'No' %>
                      </span>
                    <% else %>
                      <%= truncate(value.to_s, length: 50) %>
                    <% end %>
                  <% end %>
                </td>
              <% end %>
              <% if actions %>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <% if show_path %>
                    <%= link_to "View", show_path.call(record), 
                        class: "text-stone-600 hover:text-stone-900" %>
                  <% end %>
                  <% if edit_path %>
                    <%= link_to "Edit", edit_path.call(record), 
                        class: "text-blue-600 hover:text-blue-900" %>
                  <% end %>
                  <% if delete_path %>
                    <%= link_to "Delete", delete_path.call(record), 
                        method: :delete,
                        data: { confirm: "Are you sure?" },
                        class: "text-red-600 hover:text-red-900" %>
                  <% end %>
                </td>
              <% end %>
            </tr>
          <% end %>
        <% else %>
          <tr>
            <td colspan="<%= columns.length + (actions ? 1 : 0) + (bulk_operations ? 1 : 0) %>" class="px-6 py-12 text-center text-stone-500">
              No records found.
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
