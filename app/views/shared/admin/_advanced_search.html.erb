<%#
  Advanced search component for admin interfaces
  
  Usage:
  <%= render 'shared/admin/advanced_search',
      config: {
        text_fields: ['title', 'description'],
        date_fields: { created: 'created_at', updated: 'updated_at' },
        filters: [
          { key: 'status', label: 'Status', options: [...] },
          { key: 'category', label: 'Category', options: [...] }
        ]
      }
  %>
%>

<% 
  config ||= {}
  text_fields = config[:text_fields] || []
  date_fields = config[:date_fields] || {}
  numeric_fields = config[:numeric_fields] || {}
  filters = config[:filters] || []
  saved_searches = config[:saved_searches] || []
%>

<div class="bg-white shadow rounded-lg mb-6">
  <div class="px-4 py-5 sm:p-6">
    <!-- Toggle Advanced Search -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-4">
        <h3 class="text-lg font-medium text-stone-900">Search & Filters</h3>
        <button type="button" 
                onclick="toggleAdvancedSearch()"
                class="inline-flex items-center px-3 py-1 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50">
          <span id="advanced-search-toggle-text">
            <%= params[:advanced_search] == 'true' ? 'Simple Search' : 'Advanced Search' %>
          </span>
        </button>
      </div>
      
      <!-- Saved Searches -->
      <% if saved_searches.any? %>
        <div class="flex items-center space-x-2">
          <label for="saved_search_select" class="text-sm font-medium text-stone-700">Saved Searches:</label>
          <select id="saved_search_select" 
                  onchange="loadSavedSearch(this.value)"
                  class="rounded-md border-stone-300 text-sm">
            <option value="">Select a saved search...</option>
            <% saved_searches.each do |search| %>
              <option value="<%= search.id %>" 
                      data-params="<%= search.search_params.html_safe %>"
                      <%= 'selected' if params[:saved_search_id] == search.id.to_s %>>
                <%= search.name %>
              </option>
            <% end %>
          </select>
        </div>
      <% end %>
    </div>

    <%= form_with url: request.path, method: :get, local: true, id: "search-form", class: "space-y-4" do |form| %>
      <!-- Basic Search (always visible) -->
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
        <div class="sm:col-span-2">
          <%= form.text_field :search, 
              value: params[:search], 
              placeholder: "Search...", 
              class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" %>
        </div>
        <div>
          <%= form.submit "Search", 
              class: "w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-600 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" %>
        </div>
      </div>

      <!-- Advanced Search Panel -->
      <div id="advanced-search-panel" 
           class="<%= params[:advanced_search] == 'true' ? 'block' : 'hidden' %> border-t border-stone-200 pt-4">
        
        <%= form.hidden_field :advanced_search, value: 'true' %>
        
        <!-- Text Search -->
        <% if text_fields.any? %>
          <div class="mb-4">
            <label class="block text-sm font-medium text-stone-700 mb-2">Advanced Text Search</label>
            <%= form.text_field :text_search, 
                value: params[:text_search], 
                placeholder: "Search across all text fields...", 
                class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" %>
            <p class="mt-1 text-xs text-stone-500">
              Searches: <%= text_fields.map(&:humanize).join(', ') %>
            </p>
          </div>
        <% end %>

        <!-- Date Filters -->
        <% if date_fields.any? %>
          <div class="mb-4">
            <h4 class="text-sm font-medium text-stone-700 mb-3">Date Filters</h4>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <% date_fields.each do |field_name, column| %>
                <div class="space-y-2">
                  <label class="block text-xs font-medium text-stone-600"><%= field_name.to_s.humanize %></label>
                  <div class="grid grid-cols-2 gap-2">
                    <%= form.date_field "#{field_name}_from", 
                        value: params["#{field_name}_from"], 
                        class: "block w-full rounded-md border-stone-300 text-xs focus:border-stone-500 focus:ring-stone-500",
                        placeholder: "From" %>
                    <%= form.date_field "#{field_name}_to", 
                        value: params["#{field_name}_to"], 
                        class: "block w-full rounded-md border-stone-300 text-xs focus:border-stone-500 focus:ring-stone-500",
                        placeholder: "To" %>
                  </div>
                  <%= form.select "#{field_name}_range", 
                      options_for_select([
                        ['Select range...', ''],
                        ['Today', 'today'],
                        ['Yesterday', 'yesterday'],
                        ['This Week', 'this_week'],
                        ['Last Week', 'last_week'],
                        ['This Month', 'this_month'],
                        ['Last Month', 'last_month'],
                        ['This Year', 'this_year'],
                        ['Last Year', 'last_year']
                      ], params["#{field_name}_range"]),
                      {},
                      { class: "mt-1 block w-full rounded-md border-stone-300 text-xs focus:border-stone-500 focus:ring-stone-500" } %>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- Numeric Filters -->
        <% if numeric_fields.any? %>
          <div class="mb-4">
            <h4 class="text-sm font-medium text-stone-700 mb-3">Numeric Filters</h4>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <% numeric_fields.each do |field_name, column| %>
                <div class="space-y-2">
                  <label class="block text-xs font-medium text-stone-600"><%= field_name.to_s.humanize %></label>
                  <div class="grid grid-cols-2 gap-2">
                    <%= form.number_field "#{field_name}_min", 
                        value: params["#{field_name}_min"], 
                        class: "block w-full rounded-md border-stone-300 text-xs focus:border-stone-500 focus:ring-stone-500",
                        placeholder: "Min",
                        step: :any %>
                    <%= form.number_field "#{field_name}_max", 
                        value: params["#{field_name}_max"], 
                        class: "block w-full rounded-md border-stone-300 text-xs focus:border-stone-500 focus:ring-stone-500",
                        placeholder: "Max",
                        step: :any %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- Standard Filters -->
        <% if filters.any? %>
          <div class="mb-4">
            <h4 class="text-sm font-medium text-stone-700 mb-3">Filters</h4>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <% filters.each do |filter| %>
                <div>
                  <%= form.label filter[:key], filter[:label], class: "block text-xs font-medium text-stone-600" %>
                  <%= form.select filter[:key], 
                      options_for_select([['All', 'all']] + filter[:options], params[filter[:key]]),
                      {},
                      { class: "mt-1 block w-full rounded-md border-stone-300 text-xs focus:border-stone-500 focus:ring-stone-500" } %>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- Save Search -->
        <div class="flex items-center justify-between pt-4 border-t border-stone-200">
          <div class="flex items-center space-x-4">
            <div class="flex items-center">
              <%= form.check_box :save_search, { class: "rounded border-stone-300 text-stone-600 focus:ring-stone-500" }, "true", "" %>
              <%= form.label :save_search, "Save this search", class: "ml-2 text-sm text-stone-700" %>
            </div>
            <div class="hidden" id="search-name-field">
              <%= form.text_field :search_name, 
                  placeholder: "Search name...", 
                  class: "rounded-md border-stone-300 text-sm focus:border-stone-500 focus:ring-stone-500" %>
            </div>
          </div>
          
          <div class="flex items-center space-x-2">
            <%= link_to "Clear All", request.path, 
                class: "inline-flex items-center px-3 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50" %>
            <%= form.submit "Apply Filters", 
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-600 hover:bg-stone-700" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>

<script>
function toggleAdvancedSearch() {
  const panel = document.getElementById('advanced-search-panel');
  const toggleText = document.getElementById('advanced-search-toggle-text');
  const form = document.getElementById('search-form');
  
  if (panel.classList.contains('hidden')) {
    panel.classList.remove('hidden');
    toggleText.textContent = 'Simple Search';
    // Add hidden field for advanced search
    if (!form.querySelector('input[name="advanced_search"]')) {
      const hiddenField = document.createElement('input');
      hiddenField.type = 'hidden';
      hiddenField.name = 'advanced_search';
      hiddenField.value = 'true';
      form.appendChild(hiddenField);
    }
  } else {
    panel.classList.add('hidden');
    toggleText.textContent = 'Advanced Search';
    // Remove hidden field
    const hiddenField = form.querySelector('input[name="advanced_search"]');
    if (hiddenField) {
      hiddenField.remove();
    }
  }
}

function loadSavedSearch(searchId) {
  if (!searchId) return;
  
  const option = document.querySelector(`option[value="${searchId}"]`);
  if (option) {
    const params = JSON.parse(option.dataset.params);
    const url = new URL(window.location.href);
    
    // Clear existing params
    url.search = '';
    
    // Add saved search params
    Object.keys(params).forEach(key => {
      if (params[key]) {
        url.searchParams.set(key, params[key]);
      }
    });
    
    url.searchParams.set('saved_search_id', searchId);
    window.location.href = url.toString();
  }
}

// Show/hide search name field based on save search checkbox
document.addEventListener('DOMContentLoaded', function() {
  const saveCheckbox = document.querySelector('input[name="save_search"]');
  const nameField = document.getElementById('search-name-field');
  
  if (saveCheckbox && nameField) {
    saveCheckbox.addEventListener('change', function() {
      if (this.checked) {
        nameField.classList.remove('hidden');
      } else {
        nameField.classList.add('hidden');
      }
    });
  }
});
</script>
