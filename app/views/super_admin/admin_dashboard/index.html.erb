<% content_for :title, "Admin Dashboard" %>

<div class="max-w-7xl mx-auto">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Admin Dashboard</h1>
    <p class="mt-2 text-stone-600">Comprehensive overview of system data and activity</p>
  </div>

  <!-- System Health Status -->
  <div class="mb-8">
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-stone-900">System Health</h2>
          <div class="flex items-center">
            <% case @system_health[:overall_status] %>
            <% when :healthy %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Healthy
              </span>
            <% when :warning %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                Warning
              </span>
            <% when :unhealthy %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                Unhealthy
              </span>
            <% end %>
          </div>
        </div>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <!-- Database Health -->
          <div class="text-center">
            <div class="flex items-center justify-center w-12 h-12 mx-auto mb-2 rounded-full <%= @system_health[:database][:status] == :healthy ? 'bg-green-100' : 'bg-red-100' %>">
              <svg class="w-6 h-6 <%= @system_health[:database][:status] == :healthy ? 'text-green-600' : 'text-red-600' %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
              </svg>
            </div>
            <h3 class="text-sm font-medium text-stone-900">Database</h3>
            <p class="text-xs text-stone-500">
              <%= @system_health[:database][:response_time] ? "#{(@system_health[:database][:response_time] * 1000).round(1)}ms" : "Error" %>
            </p>
          </div>

          <!-- Search Health -->
          <div class="text-center">
            <div class="flex items-center justify-center w-12 h-12 mx-auto mb-2 rounded-full <%= @system_health[:search][:status] == :healthy ? 'bg-green-100' : (@system_health[:search][:status] == :warning ? 'bg-yellow-100' : 'bg-red-100') %>">
              <svg class="w-6 h-6 <%= @system_health[:search][:status] == :healthy ? 'text-green-600' : (@system_health[:search][:status] == :warning ? 'text-yellow-600' : 'text-red-600') %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <h3 class="text-sm font-medium text-stone-900">Search</h3>
            <p class="text-xs text-stone-500">
              <%= @system_health[:search][:indices].count %> indices
            </p>
          </div>

          <!-- Background Jobs Health -->
          <div class="text-center">
            <div class="flex items-center justify-center w-12 h-12 mx-auto mb-2 rounded-full <%= @system_health[:background_jobs][:status] == :healthy ? 'bg-green-100' : (@system_health[:background_jobs][:status] == :warning ? 'bg-yellow-100' : 'bg-red-100') %>">
              <svg class="w-6 h-6 <%= @system_health[:background_jobs][:status] == :healthy ? 'text-green-600' : (@system_health[:background_jobs][:status] == :warning ? 'text-yellow-600' : 'text-red-600') %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
              </svg>
            </div>
            <h3 class="text-sm font-medium text-stone-900">Jobs</h3>
            <p class="text-xs text-stone-500">
              <%= @system_health[:background_jobs][:pending_jobs] || 0 %> pending
            </p>
          </div>

          <!-- Cache Health -->
          <div class="text-center">
            <div class="flex items-center justify-center w-12 h-12 mx-auto mb-2 rounded-full <%= @system_health[:cache][:status] == :healthy ? 'bg-green-100' : 'bg-red-100' %>">
              <svg class="w-6 h-6 <%= @system_health[:cache][:status] == :healthy ? 'text-green-600' : 'text-red-600' %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h3 class="text-sm font-medium text-stone-900">Cache</h3>
            <p class="text-xs text-stone-500">
              <%= @system_health[:cache][:response_time] ? "#{(@system_health[:cache][:response_time] * 1000).round(1)}ms" : "Error" %>
            </p>
          </div>

          <!-- Storage Health -->
          <div class="text-center">
            <div class="flex items-center justify-center w-12 h-12 mx-auto mb-2 rounded-full <%= @system_health[:storage][:status] == :healthy ? 'bg-green-100' : 'bg-red-100' %>">
              <svg class="w-6 h-6 <%= @system_health[:storage][:status] == :healthy ? 'text-green-600' : 'text-red-600' %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
              </svg>
            </div>
            <h3 class="text-sm font-medium text-stone-900">Storage</h3>
            <p class="text-xs text-stone-500">
              <%= @system_health[:storage][:response_time] ? "#{(@system_health[:storage][:response_time] * 1000).round(1)}ms" : "Error" %>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Stats Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Users Stats -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-600 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-stone-500 truncate">Total Users</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @stats[:users][:total] %></dd>
            <dd class="text-xs text-stone-500">
              <%= @stats[:users][:verified] %> verified, 
              <%= @stats[:users][:scouts] %> scouts, 
              <%= @stats[:users][:talents] %> talents
            </dd>
          </dl>
        </div>
      </div>
    </div>

    <!-- Jobs Stats -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-600 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 002 2M8 6v2a2 2 0 002 2m0 0h4m-4 0a2 2 0 00-2 2v4a2 2 0 002 2h4a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-stone-500 truncate">Total Jobs</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @stats[:jobs][:total] %></dd>
            <dd class="text-xs text-stone-500">
              <%= @stats[:jobs][:published] %> published, 
              <%= @stats[:jobs][:draft] %> draft, 
              <%= @stats[:jobs][:expired] %> expired
            </dd>
          </dl>
        </div>
      </div>
    </div>

    <!-- Organizations Stats -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-purple-600 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-stone-500 truncate">Organizations</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @stats[:organizations][:total] %></dd>
            <dd class="text-xs text-stone-500">
              <%= @stats[:organizations][:with_jobs] %> with jobs
            </dd>
          </dl>
        </div>
      </div>
    </div>

    <!-- Communication Stats -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-orange-600 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-stone-500 truncate">Communication</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @stats[:communication][:conversations] %></dd>
            <dd class="text-xs text-stone-500">
              <%= @stats[:communication][:chat_requests] %> chat requests, 
              <%= @stats[:communication][:messages] %> messages
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Analytics Dashboard -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
    <!-- Growth Trends -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Growth Trends</h2>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Users (Weekly)</span>
              <span class="text-sm <%= @analytics[:growth_trends][:users][:weekly][:change] >= 0 ? 'text-green-600' : 'text-red-600' %>">
                <%= @analytics[:growth_trends][:users][:weekly][:change] >= 0 ? '+' : '' %><%= @analytics[:growth_trends][:users][:weekly][:change] %>%
              </span>
            </div>
            <div class="text-lg font-semibold text-stone-900"><%= @analytics[:growth_trends][:users][:weekly][:this_week] %></div>
            <div class="text-xs text-stone-500">vs <%= @analytics[:growth_trends][:users][:weekly][:last_week] %> last week</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Jobs (Weekly)</span>
              <span class="text-sm <%= @analytics[:growth_trends][:jobs][:weekly][:change] >= 0 ? 'text-green-600' : 'text-red-600' %>">
                <%= @analytics[:growth_trends][:jobs][:weekly][:change] >= 0 ? '+' : '' %><%= @analytics[:growth_trends][:jobs][:weekly][:change] %>%
              </span>
            </div>
            <div class="text-lg font-semibold text-stone-900"><%= @analytics[:growth_trends][:jobs][:weekly][:this_week] %></div>
            <div class="text-xs text-stone-500">vs <%= @analytics[:growth_trends][:jobs][:weekly][:last_week] %> last week</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Organizations (Weekly)</span>
              <span class="text-sm <%= @analytics[:growth_trends][:organizations][:weekly][:change] >= 0 ? 'text-green-600' : 'text-red-600' %>">
                <%= @analytics[:growth_trends][:organizations][:weekly][:change] >= 0 ? '+' : '' %><%= @analytics[:growth_trends][:organizations][:weekly][:change] %>%
              </span>
            </div>
            <div class="text-lg font-semibold text-stone-900"><%= @analytics[:growth_trends][:organizations][:weekly][:this_week] %></div>
            <div class="text-xs text-stone-500">vs <%= @analytics[:growth_trends][:organizations][:weekly][:last_week] %> last week</div>
          </div>
        </div>
      </div>
    </div>

    <!-- User Engagement -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">User Engagement</h2>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">7-Day Active Rate</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:user_engagement][:engagement_rate_7d] %>%</span>
            </div>
            <div class="text-xs text-stone-500"><%= @analytics[:user_engagement][:active_7_days] %> of <%= @analytics[:user_engagement][:total_users] %> users</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Verification Rate</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:user_engagement][:verification_rate] %>%</span>
            </div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Profile Completion</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:user_engagement][:profile_completion_rate] %>%</span>
            </div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Organization Join Rate</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:user_engagement][:organization_join_rate] %>%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Conversion Rates -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Conversion Rates</h2>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Job Application Acceptance</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:conversion_rates][:job_applications][:acceptance_rate] %>%</span>
            </div>
            <div class="text-xs text-stone-500"><%= @analytics[:conversion_rates][:job_applications][:accepted] %> of <%= @analytics[:conversion_rates][:job_applications][:total] %> applications</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Chat Request Acceptance</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:conversion_rates][:chat_requests][:acceptance_rate] %>%</span>
            </div>
            <div class="text-xs text-stone-500"><%= @analytics[:conversion_rates][:chat_requests][:accepted] %> of <%= @analytics[:conversion_rates][:chat_requests][:total] %> requests</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Job Application Rate</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:job_metrics][:application_rate] %>%</span>
            </div>
            <div class="text-xs text-stone-500"><%= @analytics[:job_metrics][:jobs_with_applications] %> of <%= @analytics[:job_metrics][:total_jobs] %> jobs</div>
          </div>

          <div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-stone-600">Conversation Activity</span>
              <span class="text-lg font-semibold text-stone-900"><%= @analytics[:conversion_rates][:conversations][:activity_rate] %>%</span>
            </div>
            <div class="text-xs text-stone-500"><%= @analytics[:conversion_rates][:conversations][:active_7d] %> active in 7 days</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-white rounded-lg shadow mb-8">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Quick Actions</h2>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
        <%= link_to super_admin_admin_users_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Users</h3>
            <p class="text-sm text-stone-500">Manage user accounts</p>
          </div>
        <% end %>

        <%= link_to super_admin_admin_jobs_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 002 2M8 6v2a2 2 0 002 2m0 0h4m-4 0a2 2 0 00-2 2v4a2 2 0 002 2h4a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Jobs</h3>
            <p class="text-sm text-stone-500">Manage job postings</p>
          </div>
        <% end %>

        <%= link_to super_admin_admin_chat_requests_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Chat Requests</h3>
            <p class="text-sm text-stone-500">Monitor communications</p>
          </div>
        <% end %>

        <%= link_to super_admin_admin_conversations_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h8z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Conversations</h3>
            <p class="text-sm text-stone-500">View message threads</p>
          </div>
        <% end %>

        <%= link_to super_admin_admin_organizations_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Organizations</h3>
            <p class="text-sm text-stone-500">Manage companies</p>
          </div>
        <% end %>

        <%= link_to super_admin_admin_audit_logs_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-stone-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Audit Logs</h3>
            <p class="text-sm text-stone-500">View admin activity</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Recent Admin Activity -->
  <div class="bg-white rounded-lg shadow mb-8">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Recent Admin Activity</h2>
    </div>
    <div class="overflow-hidden">
      <table class="min-w-full divide-y divide-stone-200">
        <thead class="bg-stone-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Admin</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Action</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Resource</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Time</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-stone-200">
          <% @recent_admin_actions.each do |action| %>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-stone-900"><%= action.admin_user&.name || 'Unknown' %></div>
                <div class="text-sm text-stone-500"><%= action.admin_user&.email || 'N/A' %></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                  <%= case action.action
                      when 'create' then 'bg-green-100 text-green-800'
                      when 'update' then 'bg-blue-100 text-blue-800'
                      when 'destroy' then 'bg-red-100 text-red-800'
                      else 'bg-stone-100 text-stone-800'
                      end %>">
                  <%= action.action.humanize %>
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-stone-900"><%= action.resource_type %></div>
                <div class="text-sm text-stone-500">ID: <%= action.resource_id %></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                <%= time_ago_in_words(action.created_at) %> ago
              </td>
            </tr>
          <% end %>
          <% if @recent_admin_actions.empty? %>
            <tr>
              <td colspan="4" class="px-6 py-4 text-center text-sm text-stone-500">
                No recent admin activity
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Today's Activity Summary -->
  <div class="bg-white rounded-lg shadow mb-8">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Today's Activity Summary</h2>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600"><%= @analytics[:recent_activity_summary][:new_users_today] %></div>
          <div class="text-sm text-stone-600">New Users</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600"><%= @analytics[:recent_activity_summary][:new_jobs_today] %></div>
          <div class="text-sm text-stone-600">New Jobs</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600"><%= @analytics[:recent_activity_summary][:new_applications_today] %></div>
          <div class="text-sm text-stone-600">Applications</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-orange-600"><%= @analytics[:recent_activity_summary][:new_messages_today] %></div>
          <div class="text-sm text-stone-600">Messages</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-red-600"><%= @analytics[:recent_activity_summary][:admin_actions_today] %></div>
          <div class="text-sm text-stone-600">Admin Actions</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-stone-600"><%= @analytics[:recent_activity_summary][:peak_activity_hour] %>:00</div>
          <div class="text-sm text-stone-600">Peak Hour</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Activity -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Recent Users -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Recent Users</h2>
      </div>
      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-stone-200">
          <tbody class="bg-white divide-y divide-stone-200">
            <% @recent_activity[:recent_users].each do |user| %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-8 w-8">
                      <% if user.avatar.attached? %>
                        <%= image_tag user.avatar, class: "h-8 w-8 rounded-full object-cover" %>
                      <% else %>
                        <div class="h-8 w-8 rounded-full bg-stone-300 flex items-center justify-center">
                          <span class="text-xs font-medium text-stone-700"><%= user.initials %></span>
                        </div>
                      <% end %>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-stone-900"><%= user.name %></div>
                      <div class="text-sm text-stone-500"><%= user.email %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-stone-500">
                  <%= time_ago_in_words(user.created_at) %> ago
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Recent Jobs -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Recent Jobs</h2>
      </div>
      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-stone-200">
          <tbody class="bg-white divide-y divide-stone-200">
            <% @recent_activity[:recent_jobs].each do |job| %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-stone-900"><%= truncate(job.title, length: 40) %></div>
                  <div class="text-sm text-stone-500">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= job.status == 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
                      <%= job.status.humanize %>
                    </span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm text-stone-500">
                  <%= time_ago_in_words(job.created_at) %> ago
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
