<% content_for :title, "Messages" %>

<div class="space-y-6">
  <!-- Header -->
  <div class="border-b border-stone-200 pb-5">
    <h1 class="text-2xl font-bold leading-7 text-stone-900 sm:truncate sm:text-3xl sm:tracking-tight">
      Messages
    </h1>
    <p class="mt-2 text-sm text-stone-600">
      Monitor and manage platform messages and conversations
    </p>
  </div>

  <!-- Search and Filters -->
  <%= form_with url: super_admin_admin_messages_path, method: :get, local: true, class: "bg-white shadow rounded-lg p-6 space-y-4" do |form| %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Search -->
      <div>
        <%= form.label :search, "Search", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.text_field :search, 
            value: params[:search], 
            placeholder: "Search messages, users...", 
            class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
      </div>

      <!-- Read Status Filter -->
      <div>
        <%= form.label :read_status, "Read Status", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :read_status, 
            options_for_select([
              ['All', ''],
              ['Read', 'read'],
              ['Unread', 'unread']
            ], params[:read_status]), 
            {}, 
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" } %>
      </div>

      <!-- Conversation Type Filter -->
      <div>
        <%= form.label :conversation_type, "Conversation Type", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :conversation_type, 
            options_for_select([
              ['All', ''],
              ['With Job', 'with_job'],
              ['Without Job', 'without_job']
            ], params[:conversation_type]), 
            {}, 
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" } %>
      </div>

      <!-- Per Page -->
      <div>
        <%= form.label :per_page, "Per Page", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :per_page, 
            options_for_select([
              ['25', 25],
              ['50', 50],
              ['100', 100]
            ], params[:per_page] || 25), 
            {}, 
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" } %>
      </div>
    </div>

    <!-- Date Range Filters -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <%= form.label :date_from, "From Date", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.date_field :date_from, 
            value: params[:date_from], 
            class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
      </div>
      <div>
        <%= form.label :date_to, "To Date", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.date_field :date_to, 
            value: params[:date_to], 
            class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
      </div>
    </div>

    <div class="flex justify-end">
      <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
    </div>
  <% end %>

  <!-- Results Count and Export -->
  <div class="flex justify-between items-center">
    <p class="text-sm text-stone-600">
      Showing <%= @pagy.from %> to <%= @pagy.to %> of <%= @pagy.count %> messages
    </p>
    <div class="flex space-x-2">
      <%= link_to "Export CSV", request.params.merge(format: :csv),
          class: "inline-flex items-center px-3 py-2 border border-stone-300 shadow-sm text-sm leading-4 font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
    </div>
  </div>

  <!-- Messages Table -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <table class="min-w-full divide-y divide-stone-200">
      <thead class="bg-stone-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            ID
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Sender
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Message Preview
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Conversation
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Status
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Created
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-stone-200">
        <% @messages.each do |message| %>
          <tr class="hover:bg-stone-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
              <%= message.id %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-8 w-8">
                  <div class="h-8 w-8 rounded-full bg-stone-100 flex items-center justify-center">
                    <span class="text-xs font-medium text-stone-600">
                      <%= message.user.initials %>
                    </span>
                  </div>
                </div>
                <div class="ml-3">
                  <div class="text-sm font-medium text-stone-900">
                    <%= message.user.full_name %>
                  </div>
                  <div class="text-sm text-stone-500">
                    <%= message.user.email %>
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4">
              <div class="text-sm text-stone-900 max-w-xs truncate">
                <%= message.body %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
              <div>
                <div class="font-medium">
                  Conversation #<%= message.conversation.id %>
                </div>
                <% if message.conversation.job %>
                  <div class="text-stone-500">
                    Job: <%= message.conversation.job.title %>
                  </div>
                <% else %>
                  <div class="text-stone-500">
                    Direct conversation
                  </div>
                <% end %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <% if message.read_at %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Read
                </span>
              <% else %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  Unread
                </span>
              <% end %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
              <div>
                <div><%= message.created_at.strftime("%b %d, %Y") %></div>
                <div><%= message.created_at.strftime("%I:%M %p") %></div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
              <%= link_to "View", super_admin_admin_message_path(message), 
                  class: "text-indigo-600 hover:text-indigo-900" %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <%= render 'shared/admin/pagination', pagy: @pagy %>
</div>
