<% content_for :title, "Organizations" %>

<div class="max-w-7xl mx-auto">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Organizations</h1>
    <p class="mt-2 text-stone-600">Manage company organizations and their settings</p>
  </div>

  <!-- Search and Filters -->
  <%= render 'shared/admin/search_and_filters', 
      search_placeholder: "Search by organization name...",
      filters: [
        { key: 'with_jobs', label: 'Job Status', options: @filter_options[:with_jobs] },
        { key: 'size', label: 'Company Size', options: @filter_options[:size] }
      ] %>

  <!-- Results Summary and Export -->
  <div class="mb-4 flex justify-between items-center">
    <p class="text-sm text-stone-600">
      Showing <%= @pagy.from %> to <%= @pagy.to %> of <%= @pagy.count %> organizations
    </p>
    <div class="flex space-x-2">
      <%= link_to "Export CSV", request.params.merge(format: :csv),
          class: "inline-flex items-center px-3 py-2 border border-stone-300 shadow-sm text-sm leading-4 font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
    </div>
  </div>

  <!-- Organizations Table -->
  <%= render 'shared/admin/table', 
      collection: @organizations,
      columns: [
        { 
          key: 'id', 
          label: 'ID', 
          sortable: true 
        },
        { 
          key: 'name', 
          label: 'Organization Name', 
          sortable: true,
          render: ->(organization) {
            content_tag(:div) do
              concat content_tag(:div, organization.name, class: "text-sm font-medium text-stone-900")
              if organization.operating_timezone.present?
                concat content_tag(:div, "Timezone: #{organization.operating_timezone}", class: "text-sm text-stone-500")
              end
            end
          }
        },
        { 
          key: 'size', 
          label: 'Company Size', 
          sortable: true,
          render: ->(organization) {
            if organization.size.present?
              size_class = case organization.size
                          when 'startup' then 'bg-blue-100 text-blue-800'
                          when 'small' then 'bg-green-100 text-green-800'
                          when 'medium' then 'bg-yellow-100 text-yellow-800'
                          when 'large' then 'bg-orange-100 text-orange-800'
                          when 'enterprise' then 'bg-purple-100 text-purple-800'
                          else 'bg-stone-100 text-stone-800'
                          end
              content_tag(:span, organization.size.humanize, 
                class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{size_class}")
            else
              content_tag(:span, "Not specified", class: "text-sm text-stone-500 italic")
            end
          }
        },
        { 
          key: 'members', 
          label: 'Members',
          render: ->(organization) {
            members_count = organization.organization_memberships.size
            owners_count = organization.organization_memberships.count { |m| m.org_role == 'owner' }
            admins_count = organization.organization_memberships.count { |m| m.org_role == 'admin' }
            
            content_tag(:div) do
              concat content_tag(:div, "#{members_count} total", class: "text-sm font-medium text-stone-900")
              concat content_tag(:div, "#{owners_count} owners, #{admins_count} admins", class: "text-sm text-stone-500")
            end
          }
        },
        { 
          key: 'jobs', 
          label: 'Jobs',
          render: ->(organization) {
            jobs_count = organization.jobs.size
            published_count = organization.jobs.count { |j| j.status == 'published' }
            
            content_tag(:div) do
              concat content_tag(:div, "#{jobs_count} total", class: "text-sm font-medium text-stone-900")
              if jobs_count > 0
                concat content_tag(:div, "#{published_count} published", class: "text-sm text-stone-500")
              end
            end
          }
        },
        { 
          key: 'created_at', 
          label: 'Created', 
          sortable: true,
          render: ->(organization) {
            content_tag(:div) do
              concat content_tag(:div, organization.created_at.strftime('%b %d, %Y'), class: "text-sm text-stone-900")
              concat content_tag(:div, organization.created_at.strftime('%I:%M %p'), class: "text-xs text-stone-500")
            end
          }
        },
        { 
          key: 'updated_at', 
          label: 'Last Updated', 
          sortable: true,
          render: ->(organization) {
            content_tag(:div) do
              concat content_tag(:div, organization.updated_at.strftime('%b %d, %Y'), class: "text-sm text-stone-900")
              concat content_tag(:div, organization.updated_at.strftime('%I:%M %p'), class: "text-xs text-stone-500")
            end
          }
        }
      ],
      actions: true,
      show_path: ->(organization) { super_admin_admin_organization_path(organization) },
      edit_path: ->(organization) { edit_super_admin_admin_organization_path(organization) } %>

  <!-- Pagination -->
  <%= render 'shared/admin/pagination', collection: @pagy %>
</div>
