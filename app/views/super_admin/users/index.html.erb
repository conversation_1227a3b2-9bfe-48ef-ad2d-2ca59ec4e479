<% content_for :title, "User Management" %>

<div class="max-w-7xl mx-auto" data-controller="impersonation">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">User Management</h1>
    <p class="mt-2 text-stone-600">Search, filter, and manage all users in the system</p>
  </div>

  <!-- Search and Filters -->
  <div class="bg-white rounded-lg shadow mb-6">
    <div class="p-6">
      <%= form_with url: super_admin_users_path, method: :get, local: true, class: "space-y-4" do |form| %>
        <!-- Search Row -->
        <div class="flex flex-col md:flex-row md:items-end md:space-x-4 space-y-4 md:space-y-0">
          <div class="flex-1">
            <%= form.label :q, "Search Users", class: "block text-sm font-medium text-stone-700 mb-2" %>
            <%= form.text_field :q,
                value: @q,
                placeholder: "Search by name, email, or ID...",
                class: "w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" %>
          </div>
        </div>

        <!-- Filters Row -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <%= form.label :role_filter, "Role", class: "block text-sm font-medium text-stone-700 mb-2" %>
            <%= form.select :role_filter,
                options_for_select([
                  ['All Roles', 'all'],
                  ['No Role', 'no_role']
                ] + @available_roles.map { |role| [role.titleize, role] }, @role_filter),
                {},
                { class: "w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" } %>
          </div>

          <div>
            <%= form.label :status_filter, "Status", class: "block text-sm font-medium text-stone-700 mb-2" %>
            <%= form.select :status_filter,
                options_for_select([
                  ['All Statuses', 'all'],
                  ['Verified', 'verified'],
                  ['Unverified', 'unverified'],
                  ['Onboarding Complete', 'onboarding_complete'],
                  ['Onboarding Incomplete', 'onboarding_incomplete']
                ], @status_filter),
                {},
                { class: "w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" } %>
          </div>

          <div>
            <%= form.label :organization_filter, "Organization", class: "block text-sm font-medium text-stone-700 mb-2" %>
            <%= form.select :organization_filter,
                options_for_select([
                  ['All Organizations', 'all'],
                  ['No Organization', 'no_org']
                ] + @available_organizations.map { |org| [org.name, org.id] }, @organization_filter),
                {},
                { class: "w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" } %>
          </div>

          <div>
            <%= form.label :sort_by, "Sort By", class: "block text-sm font-medium text-stone-700 mb-2" %>
            <div class="flex space-x-2">
              <%= form.select :sort_by,
                  options_for_select([
                    ['Created Date', 'created_at'],
                    ['Name', 'name'],
                    ['Email', 'email']
                  ], @sort_by),
                  {},
                  { class: "flex-1 px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" } %>
              <%= form.select :sort_direction,
                  options_for_select([
                    ['Desc', 'desc'],
                    ['Asc', 'asc']
                  ], @sort_direction),
                  {},
                  { class: "w-20 px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" } %>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-2">
          <%= form.submit "Apply Filters", class: "px-4 py-2 bg-stone-800 text-white rounded-md hover:bg-stone-900 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2" %>
          <%= link_to "Clear All", super_admin_users_path, class: "px-4 py-2 bg-stone-300 text-stone-700 rounded-md hover:bg-stone-400 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Results Summary -->
  <div class="mb-4">
    <p class="text-sm text-stone-600">
      Showing <%= @pagy.from %>-<%= @pagy.to %> of <%= @pagy.count %> users
      <% filters_applied = [@q, @role_filter, @status_filter, @organization_filter].any?(&:present?) %>
      <% if filters_applied %>
        <% filter_parts = [] %>
        <% filter_parts << "matching \"#{@q}\"" if @q.present? %>
        <% filter_parts << "with role: #{@role_filter.titleize}" if @role_filter.present? && @role_filter != 'all' %>
        <% filter_parts << "with status: #{@status_filter.humanize}" if @status_filter.present? && @status_filter != 'all' %>
        <% if @organization_filter.present? && @organization_filter != 'all' %>
          <% org_name = @organization_filter == 'no_org' ? 'No Organization' : @available_organizations.find { |o| o.id.to_s == @organization_filter }&.name %>
          <% filter_parts << "in organization: #{org_name}" if org_name %>
        <% end %>
        (<%= filter_parts.join(', ') %>)
      <% end %>
    </p>
  </div>

  <!-- Users Table -->
  <div class="bg-white rounded-lg shadow overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-stone-200">
        <thead class="bg-stone-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">User</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Email</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Roles</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Organizations</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Joined</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-stone-200">
          <% @users.each do |user| %>
            <tr class="hover:bg-stone-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <% if user.avatar.attached? && user.avatar.representable? %>
                      <%= image_tag url_for(user.avatar), class: "h-10 w-10 rounded-full object-cover", alt: user.name %>
                    <% else %>
                      <div class="h-10 w-10 rounded-full bg-stone-300 flex items-center justify-center">
                        <span class="text-sm font-medium text-stone-700"><%= user.initials %></span>
                      </div>
                    <% end %>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-stone-900">
                      <%= user.name.presence || "No name" %>
                    </div>
                    <div class="text-sm text-stone-500">ID: <%= user.id %></div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-stone-900"><%= user.email %></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex flex-wrap gap-1">
                  <% if user.roles.any? %>
                    <% user.roles.each do |role| %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-stone-200 text-stone-800">
                        <%= role.name %>
                      </span>
                    <% end %>
                  <% else %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-stone-100 text-stone-800">
                      user
                    </span>
                  <% end %>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-stone-900">
                  <% if user.organizations.any? %>
                    <%= user.organizations.pluck(:name).join(", ") %>
                  <% else %>
                    <span class="text-stone-500">None</span>
                  <% end %>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <% if user.verified? %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-stone-200 text-stone-800">
                    Verified
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-stone-200 text-stone-800">
                    Unverified
                  </span>
                <% end %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                <%= user.created_at.strftime("%b %d, %Y") %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <% unless user.superadmin? %>
                  <button type="button"
                          data-action="click->impersonation#showConfirmation"
                          data-user-id="<%= user.id %>"
                          data-user-name="<%= user.name %>"
                          data-user-email="<%= user.email %>"
                          class="text-stone-600 hover:text-stone-900 bg-transparent border-none cursor-pointer">
                    Impersonate
                  </button>
                <% else %>
                  <span class="text-stone-400">Super Admin</span>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Pagination -->
  <% if @pagy.pages > 1 %>
    <div class="mt-6 flex justify-center">
      <%== pagy_nav(@pagy) %>
    </div>
  <% end %>

  <!-- Impersonation Confirmation Modal -->
  <%= render "shared/impersonation_confirmation_modal" %>
</div>
