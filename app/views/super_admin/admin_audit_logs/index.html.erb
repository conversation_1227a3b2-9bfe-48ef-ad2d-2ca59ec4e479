<% content_for :title, "Audit Logs" %>

<div class="bg-white shadow rounded-lg">
  <div class="px-4 py-5 sm:p-6">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-stone-900">Admin Audit Logs</h1>
        <p class="mt-2 text-sm text-stone-700">
          A comprehensive log of all administrative actions performed in the system.
        </p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <%= link_to super_admin_admin_audit_logs_path(format: :csv, **request.query_parameters),
            class: "inline-flex items-center justify-center rounded-md border border-transparent bg-stone-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2" do %>
          <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Export CSV
        <% end %>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="mt-6">
      <%= form_with url: super_admin_admin_audit_logs_path, method: :get, local: true, class: "space-y-4" do |form| %>
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <!-- Search -->
          <div>
            <%= form.label :q, "Search", class: "block text-sm font-medium text-stone-700" %>
            <%= form.text_field :q, value: @q, placeholder: "Search admin, action, or controller...", 
                class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" %>
          </div>

          <!-- Action Filter -->
          <div>
            <%= form.label :action_filter, "Action", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :action_filter, 
                options_for_select([['All Actions', 'all']] + @available_actions.map { |action| [action.humanize, action] }, @action_filter),
                {}, 
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>

          <!-- Admin Filter -->
          <div>
            <%= form.label :admin_filter, "Admin User", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :admin_filter, 
                options_for_select([['All Admins', 'all']] + @available_admins.map { |admin| ["#{admin.first_name} #{admin.last_name}", admin.id] }, @admin_filter),
                {}, 
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>

          <!-- Date Range Filter -->
          <div>
            <%= form.label :date_range, "Date Range", class: "block text-sm font-medium text-stone-700" %>
            <%= form.select :date_range, 
                options_for_select([
                  ['All Time', ''],
                  ['Today', 'today'],
                  ['Yesterday', 'yesterday'],
                  ['This Week', 'this_week'],
                  ['Last Week', 'last_week'],
                  ['This Month', 'this_month'],
                  ['Last Month', 'last_month']
                ], @date_range),
                {}, 
                { class: "mt-1 block w-full rounded-md border-stone-300 shadow-sm focus:border-stone-500 focus:ring-stone-500 sm:text-sm" } %>
          </div>
        </div>

        <div class="flex items-center space-x-4">
          <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-600 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" %>
          <%= link_to "Clear", super_admin_admin_audit_logs_path, class: "inline-flex items-center px-4 py-2 border border-stone-300 text-sm font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" %>
        </div>
      <% end %>
    </div>

    <!-- Results -->
    <div class="mt-8">
      <div class="flex items-center justify-between">
        <p class="text-sm text-stone-700">
          Showing <%= @pagy.count %> of <%= @pagy.count %> results
        </p>
        <div class="flex items-center space-x-2">
          <%= link_to super_admin_admin_audit_logs_path(sort_by: 'created_at', sort_direction: (@sort_by == 'created_at' && @sort_direction == 'asc') ? 'desc' : 'asc', **request.query_parameters.except('sort_by', 'sort_direction')),
              class: "text-sm text-stone-500 hover:text-stone-700" do %>
            Sort by Date
            <% if @sort_by == 'created_at' %>
              <%= @sort_direction == 'asc' ? '↑' : '↓' %>
            <% end %>
          <% end %>
        </div>
      </div>

      <div class="mt-4 overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table class="min-w-full divide-y divide-stone-300">
          <thead class="bg-stone-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Date/Time
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Admin User
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Action
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Resource
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
                Description
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% @audit_logs.each do |log| %>
              <tr class="hover:bg-stone-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                  <%= log.created_at.strftime('%m/%d/%Y %I:%M %p') %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-stone-900">
                    <%= log.admin_name %>
                  </div>
                  <div class="text-sm text-stone-500">
                    <%= log.admin_email %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-stone-100 text-stone-800">
                    <%= log.action.humanize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                  <% if log.resource %>
                    <%= log.resource_type %>
                    <span class="text-stone-500">#<%= log.resource_id %></span>
                  <% else %>
                    <span class="text-stone-500">N/A</span>
                  <% end %>
                </td>
                <td class="px-6 py-4 text-sm text-stone-900">
                  <%= truncate(log.description, length: 100) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <%= link_to "View", super_admin_admin_audit_log_path(log), 
                      class: "text-stone-600 hover:text-stone-900" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <% if @pagy.pages > 1 %>
        <div class="mt-6">
          <%== pagy_nav(@pagy) %>
        </div>
      <% end %>
    </div>
  </div>
</div>
