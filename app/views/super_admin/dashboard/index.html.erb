<% content_for :title, "Super Admin Dashboard" %>

<div class="max-w-7xl mx-auto">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-stone-900">Super Admin Dashboard</h1>
    <p class="mt-2 text-stone-600">Manage users and monitor system activity</p>
  </div>

  <!-- Stats Grid -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-stone-600 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-stone-500 truncate">Total Users</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @user_count %></dd>
          </dl>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-stone-600 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-stone-500 truncate">Active Impersonations</dt>
            <dd class="text-lg font-medium text-stone-900"><%= @active_impersonations.count %></dd>
          </dl>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-stone-600 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-stone-500 truncate">System Status</dt>
            <dd class="text-lg font-medium text-stone-600">Operational</dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-white rounded-lg shadow mb-8">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-medium text-stone-900">Quick Actions</h2>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <%= link_to super_admin_users_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-stone-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Manage Users</h3>
            <p class="text-sm text-stone-500">View, search, and impersonate users</p>
          </div>
        <% end %>

        <%= link_to super_admin_admin_dashboard_path, class: "flex items-center p-4 border border-stone-300 rounded-lg hover:bg-stone-50 transition-colors" do %>
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-stone-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-sm font-medium text-stone-900">Admin Interface</h3>
            <p class="text-sm text-stone-500">Comprehensive data management and administration</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Recent Activity -->
  <% if @active_impersonations.any? %>
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-stone-200">
        <h2 class="text-lg font-medium text-stone-900">Active Impersonations</h2>
      </div>
      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-stone-200">
          <thead class="bg-stone-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Admin</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Impersonating</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Started</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Duration</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-stone-200">
            <% @active_impersonations.each do |log| %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                  <%= log.admin.name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                  <%= log.user.name %> (<%= log.user.email %>)
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <%= time_ago_in_words(log.started_at) %> ago
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <%= time_ago_in_words(log.started_at) %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  <% end %>
</div>
