<div class="grid grid-cols-6 gap-4 mb-6 text-sm">
  <!-- All Candidates button -->
  <% 
    # This partial expects @total_applications and @stats_total_counts to be set by the controller
    # It also relies on params[:job_id] and params[:status] for link generation and selection state
    is_all_selected = !params[:status].present?
    all_button_class = is_all_selected ? 
      "bg-stone-200 rounded-md px-4 py-2" : 
      "bg-transparent shadow border-stone-200 border rounded-md px-4 py-2"
    all_text_class = is_all_selected ? "text-stone-600" : "text-stone-600"
    all_label_class = is_all_selected ? "text-stone-500" : "text-stone-500"
  %>
  
  <%= link_to scout_applicants_path(job_id: params[:job_id]), 
      class: all_button_class,
      data: { 
        controller: "application-filter", # This controller might need to be aware of the frame update
        status: "all"
      } do %>
    <div class="flex items-center justify-between">
      <span class="<%= all_text_class %> text-lg" id="application-stage-total-count">
        <%= @total_applications %>
      </span>
    </div>
    <div class="mt-1">
      <span class="<%= all_label_class %>">Total</span>
    </div>
  <% end %>

  <!-- Status buttons -->
  <% JobApplication.statuses.keys.reject { |s| s == "withdrawn" }.each do |status| %>
    <%# Ensure @stats_total_counts is available and is a hash %>
    <% count = @stats_total_counts && @stats_total_counts[status] ? @stats_total_counts[status] : 0 %>
    <% is_selected = params[:status] == status %>
    <% 
      # Determine button styling based on selection state and count
      if is_selected
        button_class = "bg-stone-200 rounded-md px-4 py-2"
        text_class = "text-stone-600"
        label_class = "text-stone-500"
      elsif count == 0
        button_class = "bg-transparent border-stone-200 border-dashed border rounded-lg px-4 py-2"
        text_class = "text-stone-400"
        label_class = "text-stone-500"
      else
        button_class = "bg-transparent shadow border-stone-200 border rounded-lg px-4 py-2"
        text_class = "text-stone-900"
        label_class = "text-stone-600"
      end
    %>

    <% if count > 0 %>
      <%= link_to scout_applicants_path(job_id: params[:job_id], status: is_selected ? nil : status),
          class: button_class,
          data: {
            controller: "application-filter", # This controller might need to be aware of the frame update
            status: status
          } do %>
        <div class="flex items-center justify-between">
          <span class="<%= text_class %> text-lg">
            <%= count %>
          </span>
        </div>
        <div class="mt-1">
          <span class="<%= label_class %>"><%= status.titleize %></span>
        </div>
      <% end %>
    <% else %>
      <span class="<%= button_class %>" style="pointer-events: none;">
        <div class="flex items-center justify-between">
          <span class="<%= text_class %> text-lg">
            <%= count %>
          </span>
        </div>
        <div class="mt-1">
          <span class="<%= label_class %>"><%= status.titleize %></span>
        </div>
      </span>
    <% end %>
  <% end %>
</div>
