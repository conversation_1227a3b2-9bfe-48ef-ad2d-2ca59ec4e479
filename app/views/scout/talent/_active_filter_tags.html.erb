<% 
  # Collect all active filters
  active_filters = []
  
  # Bookmarked filter
  if params[:bookmarked] == 'true'
    active_filters << {
      type: 'bookmarked',
      value: 'true',
      label: 'Bookmarked Only',
      category: 'Bookmarked Talents'
    }
  end

  # Sent Requests filter
  if params[:sent_requests] == 'true'
    active_filters << {
      type: 'sent_requests',
      value: 'true',
      label: 'Sent Requests',
      category: 'Chat Requests'
    }
  end

  # Not Contacted filter
  if params[:not_contacted] == 'true'
    active_filters << {
      type: 'not_contacted',
      value: 'true',
      label: 'Not Contacted',
      category: 'Chat Requests'
    }
  end
  
  # Content Topics (niches) filter
  if params[:niches].present?
    params[:niches].split(',').each do |niche|
      active_filters << {
        type: 'niches',
        value: niche,
        label: niche,
        category: 'Content Topics'
      }
    end
  end
  
  # Ghostwriter Type filter
  if params[:ghostwriter_type].present?
    params[:ghostwriter_type].split(',').each do |type|
      active_filters << {
        type: 'ghostwriter_type',
        value: type,
        label: type,
        category: 'Ghostwriter Type'
      }
    end
  end
  
  # Availability Status filter
  if params[:availability_status].present?
    params[:availability_status].split(',').each do |status|
      active_filters << {
        type: 'availability_status',
        value: status,
        label: status.humanize,
        category: 'Availability'
      }
    end
  end

  # Skills filter (future-proofing)
  if params[:skills].present?
    params[:skills].split(',').each do |skill|
      active_filters << {
        type: 'skills',
        value: skill,
        label: skill,
        category: 'Skills'
      }
    end
  end

  # Location Preference filter (future-proofing)
  if params[:location_preference].present?
    params[:location_preference].split(',').each do |location|
      active_filters << {
        type: 'location_preference',
        value: location,
        label: location,
        category: 'Location Preference'
      }
    end
  end

  # Pricing Model filter (future-proofing)
  if params[:pricing_model].present?
    params[:pricing_model].split(',').each do |model|
      active_filters << {
        type: 'pricing_model',
        value: model,
        label: model.humanize,
        category: 'Pricing Model'
      }
    end
  end

  # Location filter (future-proofing)
  if params[:location].present?
    params[:location].split(',').each do |location|
      active_filters << {
        type: 'location',
        value: location,
        label: location,
        category: 'Location'
      }
    end
  end

  # Is Agency filter (future-proofing)
  if params[:is_agency].present?
    active_filters << {
      type: 'is_agency',
      value: params[:is_agency],
      label: params[:is_agency] == 'true' ? 'Agencies Only' : 'Individuals Only',
      category: 'Type'
    }
  end
%>

<% if active_filters.any? %>
  <div class="flex flex-wrap items-center gap-2" data-talent-filter-tags-target="container">
    <% active_filters.each do |filter| %>
      <span class="inline-flex items-center px-3 py-1.5 rounded-md text-sm bg-stone-50 border border-stone-200 text-stone-700 hover:bg-stone-100 transition-colors duration-150">
        <span class="font-medium text-xs"><%= filter[:label] %></span>
        <button 
          type="button" 
          class="ml-2 text-stone-400 hover:text-stone-600 transition-colors duration-150" 
          data-action="click->talent-filter-tags#removeFilter" 
          data-filter-type="<%= filter[:type] %>" 
          data-filter-value="<%= filter[:value] %>"
          title="Remove <%= filter[:label] %> filter"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </span>
    <% end %>
    
    <!-- Clear All Filters Button -->
    <button 
      type="button" 
      class="inline-flex items-center px-3 py-1.5 text-sm text-stone-600 hover:text-stone-800 underline transition-colors duration-150"
      data-action="click->talent-filter-tags#clearAllFilters"
    >
      Clear all filters
    </button>
  </div>
<% end %>
