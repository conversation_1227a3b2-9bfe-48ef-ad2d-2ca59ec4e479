<div class="flex flex-col h-full bg-white border rounded-md border-stone-200">
  <div class="flex-1 w-full py-8 mx-auto max-w-7xl">

    <div class="flex items-start justify-between px-16 mb-6">
      <div>
        <h1 class="text-2xl font-semibold text-stone-900"><%= @talent_profile.user.name&.full %></h1>
        <% if @talent_profile.headline.present? %>
          <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500"><%= @talent_profile.headline %></p>
        <% end %>
      </div>
      <div class="flex flex-col space-y-2">
        <%# link_to 'Send Message', new_scout_conversation_path(recipient_id: @talent_profile.user.id), class: "inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-black rounded-md shadow-sm hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %> <%# Placeholder for 'Request Application' - functionality needs to be defined %> <%# link_to 'Request Application', '#', class: "inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-stone-700 bg-stone-100 rounded-md shadow-sm hover:bg-stone-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
      </div>
    </div>

    <div class="px-16">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Personal Details</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Talent's personal details.</p>
      </div>

      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Full name</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @talent_profile.user.name&.full %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Headline</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @talent_profile.headline %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Availability</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% case @talent_profile.availability_status %>
              <% when "available" %>
                <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">Available</span>
              <% when "limited" %>
                <span class="px-2 py-1 text-xs font-medium text-yellow-800 bg-yellow-100 rounded-full">Limited Availability</span>
              <% when "unavailable" %>
                <span class="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">Unavailable</span>
              <% else %>
                <span class="text-stone-500"><%= @talent_profile.availability_status&.humanize || "Not specified" %></span>
              <% end %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Agency or Independent?</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @talent_profile.is_agency ? "Agency" : "Independent Ghostwriter" %>
            </dd>
          </div>
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Location</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.location.present? %>
                <%= case @talent_profile.location
                    when "north_america" then "North America"
                    when "united_kingdom" then "United Kingdom"
                    when "europe" then "Europe"
                    when "africa" then "Africa"
                    when "asia" then "Asia"
                    when "south_america" then "South America"
                    when "india" then "India"
                    else @talent_profile.location.humanize
                    end %>
              <% else %>
                <span class="text-stone-500">Not specified</span>
              <% end %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <div class="px-16 pt-8">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Professional Details</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Talent's professional details.</p>
      </div>
      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">About</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= simple_format(@talent_profile.about) if @talent_profile.about.present? %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Looking for</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <%= @talent_profile.looking_for if @talent_profile.looking_for.present? %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Achievement Badges</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.achievement_badges.present? && @talent_profile.achievement_badges.any? %>
                <div class="flex flex-wrap gap-2">
                  <% @talent_profile.achievement_badges.reject(&:blank?).each do |badge| %>
                    <span class="px-3 py-1 text-sm font-medium text-purple-800 bg-purple-100 rounded-full"><%= badge %></span>
                  <% end %>
                </div>
              <% else %>
                <span class="text-stone-500">Not specified</span>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Type of Ghostwriter</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.ghostwriter_type.present? && @talent_profile.ghostwriter_type.any? { |t| t.present? } %>
                <div class="flex flex-wrap gap-2">
                  <% @talent_profile.ghostwriter_type.reject(&:blank?).each do |type| %>
                    <span class="px-3 py-1 text-sm font-medium text-indigo-800 bg-indigo-100 rounded-full"><%= type %></span>
                  <% end %>
                </div>
              <% else %>
                <span class="text-stone-500">Not specified</span>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Skills / Preferred Content Topics</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.skills.present? && @talent_profile.skills.any? %>
                <div class="flex flex-wrap gap-2">
                  <% @talent_profile.skills.reject(&:blank?).each do |skill| %>
                    <span class="px-3 py-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full"><%= skill %></span>
                  <% end %>
                </div>
              <% elsif @talent_profile.niches.present? && @talent_profile.niches.any? %>
                <div class="flex flex-wrap gap-2">
                  <% @talent_profile.niches.reject(&:blank?).each do |niche| %>
                    <span class="px-3 py-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-full"><%= niche %></span>
                  <% end %>
                </div>
              <% else %>
                <span class="text-stone-500">Not specified</span>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Outcomes I can achieve:</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.outcomes.present? && @talent_profile.outcomes.any? { |o| o.present? } %>
                <div class="flex flex-wrap gap-2">
                  <% @talent_profile.outcomes.reject(&:blank?).each do |outcome| %>
                    <span class="px-3 py-1 text-sm font-medium text-green-800 bg-green-100 rounded-full"><%= outcome %></span>
                  <% end %>
                </div>
              <% else %>
                <span class="text-stone-500">Not specified</span>
              <% end %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <div class="px-16 pt-8">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Social Media & Platforms</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Social media profiles and platform information.</p>
      </div>
      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Social Media Specialty</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.social_media_specialty.present? && @talent_profile.social_media_specialty.any? { |s| s.present? } %>
                <div class="flex flex-wrap gap-2">
                  <% @talent_profile.social_media_specialty.reject(&:blank?).each do |specialty| %>
                    <span class="px-3 py-1 text-sm font-medium text-purple-800 bg-purple-100 rounded-full"><%= specialty %></span>
                  <% end %>
                </div>
              <% else %>
                <span class="text-stone-500">Not specified</span>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Website</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.website_url.present? %>
                <a href="<%= @talent_profile.website_url %>" target="_blank" rel="noopener noreferrer" class="flex items-center text-blue-600 hover:text-blue-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="2" y1="12" x2="22" y2="12"></line>
                    <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                  </svg>
                  Personal Website
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="7" y1="17" x2="17" y2="7"></line>
                    <polyline points="7 7 17 7 17 17"></polyline>
                  </svg>
                </a>
              <% else %>
                <span class="text-stone-500">Not provided</span>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Portfolio</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.portfolio_link.present? %>
                <a href="<%= @talent_profile.portfolio_link %>" target="_blank" rel="noopener noreferrer" class="flex items-center text-blue-600 hover:text-blue-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                  </svg>
                  View Portfolio
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="7" y1="17" x2="17" y2="7"></line>
                    <polyline points="7 7 17 7 17 17"></polyline>
                  </svg>
                </a>
              <% else %>
                <span class="text-stone-500">Not provided</span>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">LinkedIn</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.linkedin_url.present? %>
                <a href="<%= @talent_profile.linkedin_url %>" target="_blank" rel="noopener noreferrer" class="flex items-center text-blue-600 hover:text-blue-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                  LinkedIn Profile
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="7" y1="17" x2="17" y2="7"></line>
                    <polyline points="7 7 17 7 17 17"></polyline>
                  </svg>
                </a>
              <% else %>
                <span class="text-stone-500">Not provided</span>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">X (Twitter)</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.x_url.present? %>
                <a href="<%= @talent_profile.x_url %>" target="_blank" rel="noopener noreferrer" class="flex items-center text-blue-600 hover:text-blue-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                  X Profile
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="7" y1="17" x2="17" y2="7"></line>
                    <polyline points="7 7 17 7 17 17"></polyline>
                  </svg>
                </a>
              <% else %>
                <span class="text-stone-500">Not provided</span>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Instagram</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.instagram_url.present? %>
                <a href="<%= @talent_profile.instagram_url %>" target="_blank" rel="noopener noreferrer" class="flex items-center text-blue-600 hover:text-blue-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
                  </svg>
                  Instagram Profile
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="7" y1="17" x2="17" y2="7"></line>
                    <polyline points="7 7 17 7 17 17"></polyline>
                  </svg>
                </a>
              <% else %>
                <span class="text-stone-500">Not provided</span>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Threads</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.threads_url.present? %>
                <a href="<%= @talent_profile.threads_url %>" target="_blank" rel="noopener noreferrer" class="flex items-center text-blue-600 hover:text-blue-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12.186 24h-.007c-3.581-.024-6.334-1.205-8.184-3.509C2.35 18.44 1.5 15.586 1.472 12.01l.03-.705c.11-2.37.69-4.385 1.727-5.999C4.266 3.692 5.77 2.435 7.717 1.637c1.168-.479 2.47-.785 3.74-.908a5.822 5.822 0 0 1 1.063-.021h.36c2.156.088 4.099.681 5.778 1.772a9.063 9.063 0 0 1 3.237 3.64c.808 1.675 1.169 3.61 1.075 5.772-.071 1.773-.62 3.481-1.563 4.856-1.063 1.56-2.645 2.74-4.474 3.338a8.17 8.17 0 0 1-1.399.027 6.018 6.018 0 0 1-3.421-1.505c.032 1.92.226 2.658.377 2.955.026.051.057.137.106.152.03.01.072.006.095.006.134-.014.299-.092.452-.164l.04-.019c.83-.403 1.696-.82 3.322-.82 1.198 0 1.703.267 1.788.346.218.195.327.538.337 1.061.001.133-.028.293-.08.463-.135.445-.415.794-.836 1.041-.67.394-1.55.574-2.777.574zM8.675 10.58c-.783.146-1.439.819-1.56 1.65-.161 1.101.72 2.087 1.975 2.222.17.018.342.018.515.018 1.65 0 2.648-1.518 2.848-3.101-.202-.65-.866-1.306-1.617-1.306-.334 0-.616.072-.873.35-.282.305-.518.346-.7.346-.322 0-.731-.198-.764-.852-.004-.06-.004-.118-.004-.178 0-1.668 1.49-3.02 3.318-3.02h.044c1.922.031 3.105 1.236 3.293 3.355a20.873 20.873 0 0 1 .019 1.228c-.019 2.126-.615 5.122-2.922 5.122a3.25 3.25 0 0 1-.607-.056c-1.987-.368-2.313-2.275-2.313-2.621 0-.658.575-1.157 1.348-1.157zm3.234-3.364c-.025 0-.05 0-.075.004-.765.037-1.47.46-1.857 1.11-.223.374-.326.787-.308 1.229.007.158.025.328.024.362.96-.573 2.125-.617 2.936.024-.12-.763-.487-2.726-.72-2.729zm5.919 1.79c-1.434 0-1.785.596-1.835.765.357.237.684.69.755 1.377.212-.56 1.056-1.048 1.887-.844a1.05 1.05 0 0 1 .786 1.399c-.197.543-.756.896-1.337.84-.484-.047-.89-.396-1.073-.92-.21.163-.4.385-.573.667 1.42 1.137 2.97.535 3.578-.177a2.253 2.253 0 0 0 .46-2.807c-.544-.95-1.693-1.326-2.648-1.3zm-3.275 7.146c.243.363.696.527 1.067.539.93.028 1.05-.752 1.04-1.222h-.005c-.18-.753-.77-1.224-1.477-1.224a1.236 1.236 0 0 0-.439.081c-.273.405-.274 1.457-.186 1.826z"/>
                  </svg>
                  Threads Profile
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="7" y1="17" x2="17" y2="7"></line>
                    <polyline points="7 7 17 7 17 17"></polyline>
                  </svg>
                </a>
              <% else %>
                <span class="text-stone-500">Not provided</span>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Video Sales Letter (VSL)</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.vsl_link.present? %>
                <a href="<%= @talent_profile.vsl_link %>" target="_blank" rel="noopener noreferrer" class="flex items-center text-blue-600 hover:text-blue-800">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18"></rect>
                    <line x1="10" y1="8" x2="10" y2="16"></line>
                    <line x1="14" y1="8" x2="14" y2="16"></line>
                  </svg>
                  Watch VSL
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 ml-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="7" y1="17" x2="17" y2="7"></line>
                    <polyline points="7 7 17 7 17 17"></polyline>
                  </svg>
                </a>
              <% else %>
                <span class="text-stone-500">Not provided</span>
              <% end %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <div class="px-16 pt-8">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Pricing Details</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Pricing information and rate details.</p>
      </div>
      <div class="mt-6 border-t border-stone-100">
        <dl class="divide-y divide-stone-100">
          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Pricing Model</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% case @talent_profile.pricing_model %>
              <% when "hourly" %>
                Hourly Rate
              <% when "fixed_price" %>
                Fixed Price
              <% when "retainer" %>
                Retainer
              <% when "project_based" %>
                Project Based
              <% else %>
                <span class="text-stone-500"><%= @talent_profile.pricing_model&.humanize || "Not specified" %></span>
              <% end %>
            </dd>
          </div>

          <div class="py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-stone-900">Price Range</dt>
            <dd class="mt-1 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
              <% if @talent_profile.price_range_min.present? && @talent_profile.price_range_max.present? %>
                <% if @talent_profile.pricing_model == "hourly" %>
                  $<%= number_with_precision(@talent_profile.price_range_min, precision: 0) %> - $<%= number_with_precision(@talent_profile.price_range_max, precision: 0) %> USD per hour
                <% else %>
                  $<%= number_with_precision(@talent_profile.price_range_min, precision: 0) %> - $<%= number_with_precision(@talent_profile.price_range_max, precision: 0) %> USD
                <% end %>
              <% else %>
                <span class="text-stone-500">Not specified</span>
              <% end %>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <div class="px-16 pt-8">
      <div class="sm:px-0">
        <h3 class="text-base font-semibold leading-7 text-stone-900">Attachments</h3>
        <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Resume, cover letter, and other documents.</p>
      </div>
      <div class="mt-6 border-t border-stone-100">
        <div class="py-6 sm:px-0">
          <% if @talent_profile.attachments.attached? && @talent_profile.attachments.any? %>
            <ul role="list" class="border divide-y rounded-md border-stone-200 divide-stone-100">
              <% @talent_profile.attachments.each do |attachment| %>
                <li class="flex items-center justify-between py-4 pl-4 pr-5 text-sm leading-6">
                  <div class="flex items-center flex-1 w-0">
                    <svg class="flex-shrink-0 w-5 h-5 text-stone-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M15.621 4.379a3 3 0 00-4.242 0l-7 7a3 3 0 004.241 4.243h.001l.497-.5a.75.75 0 011.064 1.057l-.498.501-.002.002a4.5 4.5 0 01-6.364-6.364l7-7a4.5 4.5 0 016.368 6.36l-3.455 3.553A2.625 2.625 0 119.52 9.52l3.45-3.451a.75.75 0 111.061 1.06l-3.45 3.451a1.125 1.125 0 001.587 1.595l3.454-3.553a3 3 0 000-4.242z" clip-rule="evenodd" />
                    </svg>
                    <div class="flex flex-1 min-w-0 gap-2 ml-4">
                      <span class="font-medium truncate"><%= attachment.filename %></span>
                      <span class="flex-shrink-0 text-stone-400"><%= number_to_human_size(attachment.byte_size) %></span>
                    </div>
                  </div>
                  <div class="flex-shrink-0 ml-4">
                    <%= link_to "Download", rails_blob_path(attachment, disposition: "attachment"), class: "font-medium text-blue-600 hover:text-blue-500" %>
                  </div>
                </li>
              <% end %>
            </ul>
          <% else %>
            <div class="py-6 text-center text-stone-500">
              <p>No attachments available</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

  </div>
</div>
