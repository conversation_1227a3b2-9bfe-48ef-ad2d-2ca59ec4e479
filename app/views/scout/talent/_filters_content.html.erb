<div class="flex flex-wrap items-center gap-3">
  <!-- Bookmarked Talents Section -->
  <div class="relative filter-section" data-controller="dropdown">
    <button type="button" class="flex items-center justify-between min-w-[160px] px-3 py-2 text-sm font-medium text-stone-700 bg-white border border-stone-300 rounded-md shadow-sm hover:bg-stone-50 hover:border-stone-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-900 transition-all duration-200" data-dropdown-target="button" data-action="click->dropdown#toggle">
      <span>Bookmarked Talents</span>
      <svg class="w-4 h-4 ml-2 text-stone-400 transition-transform duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-dropdown-target="chevron">
        <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
      </svg>
    </button>
    <div class="absolute z-20 hidden w-80 mt-1 bg-white rounded-md shadow-lg border border-stone-200" data-dropdown-target="menu">
      <div class="p-4 space-y-2">
        <% current_bookmarked = params[:bookmarked] == 'true' %>
        <%= link_to scout_talent_index_path(bookmarked: !current_bookmarked),
            class: "flex items-center gap-3 p-2 rounded-md hover:bg-stone-50 transition-colors duration-150",
            data: { turbo: false } do %>
          <div class="relative">
            <input type="checkbox" <%= 'checked' if current_bookmarked %> class="w-4 h-4 text-stone-600 bg-stone-100 border-stone-300 rounded focus:ring-stone-900 focus:ring-2" readonly>
          </div>
          <span class="flex-1 text-sm font-medium text-stone-700">Show Bookmarked Only</span>
          <span class="text-xs text-stone-500">(<%= Current.user.bookmarked_talents.count %>)</span>
        <% end %>
      </div>
      <div class="flex items-center justify-between p-4 border-t border-stone-100 bg-stone-50 rounded-b-md">
        <%= link_to scout_talent_index_path(bookmarked: nil),
            class: "text-sm text-stone-600 hover:text-stone-800 transition-colors duration-150",
            data: { turbo: false } do %>
          Clear all
        <% end %>
        <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-stone-900 rounded-md hover:bg-stone-800 transition-colors duration-150" data-action="click->dropdown#toggle">
          Apply
        </button>
      </div>
    </div>
  </div>

  <!-- Sent Requests Section -->
  <div class="relative filter-section" data-controller="dropdown">
    <button type="button" class="flex items-center justify-between min-w-[140px] px-3 py-2 text-sm font-medium text-stone-700 bg-white border border-stone-300 rounded-md shadow-sm hover:bg-stone-50 hover:border-stone-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-900 transition-all duration-200" data-dropdown-target="button" data-action="click->dropdown#toggle">
      <span>Sent Requests</span>
      <svg class="w-4 h-4 ml-2 text-stone-400 transition-transform duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-dropdown-target="chevron">
        <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
      </svg>
    </button>
    <div class="absolute z-20 hidden w-80 mt-1 bg-white rounded-md shadow-lg border border-stone-200" data-dropdown-target="menu">
      <div class="p-4 space-y-2">
        <% current_sent_requests = params[:sent_requests] == 'true' %>
        <% current_not_contacted = params[:not_contacted] == 'true' %>

        <!-- Sent Requests Option -->
        <%= link_to scout_talent_index_path(sent_requests: !current_sent_requests, not_contacted: nil),
            class: "flex items-center gap-3 p-2 rounded-md hover:bg-stone-50 transition-colors duration-150",
            data: { turbo: false } do %>
          <div class="relative">
            <input type="checkbox" <%= 'checked' if current_sent_requests %> class="w-4 h-4 text-stone-600 bg-stone-100 border-stone-300 rounded focus:ring-stone-900 focus:ring-2" readonly>
          </div>
          <span class="flex-1 text-sm font-medium text-stone-700">Show Sent Requests Only</span>
          <span class="text-xs text-stone-500">(<%= Current.user.sent_chat_requests.select(:talent_id).distinct.count %>)</span>
        <% end %>

        <!-- Not Contacted Option -->
        <%= link_to scout_talent_index_path(not_contacted: !current_not_contacted, sent_requests: nil),
            class: "flex items-center gap-3 p-2 rounded-md hover:bg-stone-50 transition-colors duration-150",
            data: { turbo: false } do %>
          <div class="relative">
            <input type="checkbox" <%= 'checked' if current_not_contacted %> class="w-4 h-4 text-stone-600 bg-stone-100 border-stone-300 rounded focus:ring-stone-900 focus:ring-2" readonly>
          </div>
          <span class="flex-1 text-sm font-medium text-stone-700">Show Not Contacted Only</span>
          <%
            contacted_count = Current.user.sent_chat_requests.select(:talent_id).distinct.count
            total_talent_count = TalentProfile.count
            not_contacted_count = total_talent_count - contacted_count
          %>
          <span class="text-xs text-stone-500">(<%= not_contacted_count %>)</span>
        <% end %>
      </div>
      <div class="flex items-center justify-between p-4 border-t border-stone-100 bg-stone-50 rounded-b-md">
        <%= link_to scout_talent_index_path(sent_requests: nil, not_contacted: nil),
            class: "text-sm text-stone-600 hover:text-stone-800 transition-colors duration-150",
            data: { turbo: false } do %>
          Clear all
        <% end %>
        <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-stone-900 rounded-md hover:bg-stone-800 transition-colors duration-150" data-action="click->dropdown#toggle">
          Apply
        </button>
      </div>
    </div>
  </div>

  <!-- Content Topics Section -->
  <div class="relative filter-section" data-controller="dropdown">
    <button type="button" class="flex items-center justify-between min-w-[140px] px-3 py-2 text-sm font-medium text-stone-700 bg-white border border-stone-300 rounded-md shadow-sm hover:bg-stone-50 hover:border-stone-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-900 transition-all duration-200" data-dropdown-target="button" data-action="click->dropdown#toggle">
      <span>Content Topics</span>
      <svg class="w-4 h-4 ml-2 text-stone-400 transition-transform duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-dropdown-target="chevron">
        <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
      </svg>
    </button>
    <div class="absolute z-20 hidden w-80 mt-1 bg-white rounded-md shadow-lg border border-stone-200 max-h-96 overflow-hidden" data-dropdown-target="menu">
      <div class="p-4 space-y-1 max-h-80 overflow-y-auto">
        <% @aggregations["niches"]["buckets"].each do |bucket| %>
          <% current_niches = params[:niches].to_s.split(',') %>
          <% is_selected = current_niches.include?(bucket["key"]) %>
          <% new_niches = is_selected ? current_niches - [bucket["key"]] : current_niches + [bucket["key"]] %>
          <% new_params = request.params.deep_dup.merge(niches: new_niches.any? ? new_niches.join(',') : nil) %>
          <%= link_to scout_talent_index_path(new_params),
              class: "flex items-center gap-3 p-2 rounded-md hover:bg-stone-50 transition-colors duration-150",
              data: { turbo: false } do %>
            <div class="relative">
              <input type="checkbox" <%= 'checked' if is_selected %> class="w-4 h-4 text-stone-600 bg-stone-100 border-stone-300 rounded focus:ring-stone-900 focus:ring-2" readonly>
            </div>
            <span class="flex-1 text-sm font-medium text-stone-700"><%= bucket["key"] %></span>
            <span class="text-xs text-stone-500">(<%= bucket["doc_count"] || 0 %>)</span>
          <% end %>
        <% end %>
      </div>
      <div class="flex items-center justify-between p-4 border-t border-stone-100 bg-stone-50 rounded-b-md">
        <%= link_to scout_talent_index_path(request.params.deep_dup.merge(niches: nil)),
            class: "text-sm text-stone-600 hover:text-stone-800 transition-colors duration-150",
            data: { turbo: false } do %>
          Clear all
        <% end %>
        <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-stone-900 rounded-md hover:bg-stone-800 transition-colors duration-150" data-action="click->dropdown#toggle">
          Apply
        </button>
      </div>
    </div>
  </div>

  <!-- Ghostwriter Type Section -->
  <div class="relative filter-section" data-controller="dropdown">
    <button type="button" class="flex items-center justify-between min-w-[150px] px-3 py-2 text-sm font-medium text-stone-700 bg-white border border-stone-300 rounded-md shadow-sm hover:bg-stone-50 hover:border-stone-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-900 transition-all duration-200" data-dropdown-target="button" data-action="click->dropdown#toggle">
      <span>Ghostwriter Type</span>
      <svg class="w-4 h-4 ml-2 text-stone-400 transition-transform duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-dropdown-target="chevron">
        <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
      </svg>
    </button>
    <div class="absolute z-20 hidden w-80 mt-1 bg-white rounded-md shadow-lg border border-stone-200 max-h-96 overflow-hidden" data-dropdown-target="menu">
      <div class="p-4 space-y-1 max-h-80 overflow-y-auto">
        <% @aggregations["ghostwriter_type"]["buckets"].each do |bucket| %>
          <% current_ghostwriter_types = params[:ghostwriter_type].to_s.split(',') %>
          <% is_selected = current_ghostwriter_types.include?(bucket["key"]) %>
          <% new_ghostwriter_types = is_selected ? current_ghostwriter_types - [bucket["key"]] : current_ghostwriter_types + [bucket["key"]] %>
          <% new_params = request.params.deep_dup.merge(ghostwriter_type: new_ghostwriter_types.any? ? new_ghostwriter_types.join(',') : nil) %>
          <%= link_to scout_talent_index_path(new_params),
              class: "flex items-center gap-3 p-2 rounded-md hover:bg-stone-50 transition-colors duration-150",
              data: { turbo: false } do %>
            <div class="relative">
              <input type="checkbox" <%= 'checked' if is_selected %> class="w-4 h-4 text-stone-600 bg-stone-100 border-stone-300 rounded focus:ring-stone-900 focus:ring-2" readonly>
            </div>
            <span class="flex-1 text-sm font-medium text-stone-700"><%= bucket["key"] %></span>
            <span class="text-xs text-stone-500">(<%= bucket["doc_count"] || 0 %>)</span>
          <% end %>
        <% end %>
      </div>
      <div class="flex items-center justify-between p-4 border-t border-stone-100 bg-stone-50 rounded-b-md">
        <%= link_to scout_talent_index_path(request.params.deep_dup.merge(ghostwriter_type: nil)),
            class: "text-sm text-stone-600 hover:text-stone-800 transition-colors duration-150",
            data: { turbo: false } do %>
          Clear all
        <% end %>
        <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-stone-900 rounded-md hover:bg-stone-800 transition-colors duration-150" data-action="click->dropdown#toggle">
          Apply
        </button>
      </div>
    </div>
  </div>

  <!-- Availability Section -->
  <div class="relative filter-section" data-controller="dropdown">
    <button type="button" class="flex items-center justify-between min-w-[120px] px-3 py-2 text-sm font-medium text-stone-700 bg-white border border-stone-300 rounded-md shadow-sm hover:bg-stone-50 hover:border-stone-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-900 transition-all duration-200" data-dropdown-target="button" data-action="click->dropdown#toggle">
      <span>Availability</span>
      <svg class="w-4 h-4 ml-2 text-stone-400 transition-transform duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-dropdown-target="chevron">
        <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
      </svg>
    </button>
    <div class="absolute z-20 hidden w-80 mt-1 bg-white rounded-md shadow-lg border border-stone-200" data-dropdown-target="menu">
      <div class="p-4 space-y-1">
        <% if @aggregations["availability_status"] && @aggregations["availability_status"]["buckets"] %>
          <% @aggregations["availability_status"]["buckets"].each do |bucket| %>
            <% current_availability_statuses = params[:availability_status].to_s.split(',') %>
            <% is_selected = current_availability_statuses.include?(bucket["key"]) %>
            <% new_availability_statuses = is_selected ? current_availability_statuses - [bucket["key"]] : current_availability_statuses + [bucket["key"]] %>
            <% new_params = request.params.deep_dup.merge(availability_status: new_availability_statuses.any? ? new_availability_statuses.join(',') : nil) %>
            <%= link_to scout_talent_index_path(new_params),
                class: "flex items-center gap-3 p-2 rounded-md hover:bg-stone-50 transition-colors duration-150",
                data: { turbo: false } do %>
              <div class="relative">
                <input type="checkbox" <%= 'checked' if is_selected %> class="w-4 h-4 text-stone-600 bg-stone-100 border-stone-300 rounded focus:ring-stone-900 focus:ring-2" readonly>
              </div>
              <span class="flex-1 text-sm font-medium text-stone-700"><%= bucket["key"].humanize %></span>
              <span class="text-xs text-stone-500">(<%= bucket["doc_count"] || 0 %>)</span>
            <% end %>
          <% end %>
        <% else %>
          <div style="color: red; padding: 10px;">
            No availability_status aggregation data found
          </div>
        <% end %>
      </div>
      <div class="flex items-center justify-between p-4 border-t border-stone-100 bg-stone-50 rounded-b-md">
        <%= link_to scout_talent_index_path(request.params.deep_dup.merge(availability_status: nil)),
            class: "text-sm text-stone-600 hover:text-stone-800 transition-colors duration-150",
            data: { turbo: false } do %>
          Clear all
        <% end %>
        <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-stone-900 rounded-md hover:bg-stone-800 transition-colors duration-150" data-action="click->dropdown#toggle">
          Apply
        </button>
      </div>
    </div>
  </div>

</div>
